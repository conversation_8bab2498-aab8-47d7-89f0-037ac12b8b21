{"name": "fst-scraper", "module": "index.ts", "type": "module", "private": true, "scripts": {"start": "bun index.ts", "cli": "bun src/cli.ts", "scrape:start": "bun src/cli.ts start", "scrape:status": "bun src/cli.ts status", "scrape:reset": "bun src/cli.ts reset"}, "devDependencies": {"@types/bun": "latest", "@types/fs-extra": "^11.0.4"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"cheerio": "^1.1.2", "fs-extra": "^11.3.0", "playwright": "^1.54.1"}}