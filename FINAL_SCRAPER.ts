import { chromium, Browser, <PERSON> } from 'playwright';
import * as fs from 'fs-extra';
import * as path from 'path';

interface ProductInfo {
  fstItemNo: string;
  legacyItemNo?: string;
  successorItem?: string;
  variant?: string;
  netWeight?: string;
  packaging?: string;
  reach?: string;
  rohs?: string;
  brand?: string;
  material?: string;
  title: string;
  description: string;
  specifications: Record<string, string>;
  dimensions: Record<string, string>;
  images: string[];
  category: string;
  subcategory: string;
  url: string;
  scrapedAt: string;
}

interface SubcategoryInfo {
  name: string;
  url: string;
  description?: string;
  images: string[];
  products: ProductInfo[];
  totalPages: number;
  scrapedAt?: string;
}

class FinalFSTScraper {
  private browser?: Browser;
  private page?: Page;
  private outputDir: string;

  constructor(outputDir: string = './data') {
    this.outputDir = outputDir;
    fs.ensureDirSync(outputDir);
  }

  async start(): Promise<void> {
    try {
      console.log('🚀 Запуск ФИНАЛЬНОГО FST скрапера...');
      
      await this.initBrowser();
      const categories = await this.getMainCategories();
      
      console.log(`📂 Найдено ${categories.length} основных категорий`);
      
      for (const category of categories) {
        await this.processCategory(category);
      }
      
      console.log('🎉 Скрапинг завершен!');
      
    } catch (error) {
      console.error('💥 Ошибка:', error);
    } finally {
      await this.closeBrowser();
    }
  }

  private async initBrowser(): Promise<void> {
    this.browser = await chromium.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-dev-shm-usage']
    });
    
    this.page = await this.browser.newPage({
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    });
    
    await this.page.setViewportSize({ width: 1920, height: 1080 });
    this.page.setDefaultTimeout(60000);
  }

  private async closeBrowser(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
    }
  }

  private async getMainCategories(): Promise<Array<{name: string, url: string, subcategories: SubcategoryInfo[]}>> {
    if (!this.page) throw new Error('Browser not initialized');

    await this.page.goto('https://products.fst.com/global/en', { 
      waitUntil: 'domcontentloaded',
      timeout: 60000 
    });
    
    await this.page.waitForTimeout(5000);

    const mainCategoryLinks = await this.page.$$eval('a[href*="/categories/"]', links => {
      return links.map(link => ({
        name: link.textContent?.trim() || '',
        url: (link as HTMLAnchorElement).href
      })).filter(cat => {
        const hasName = cat.name && cat.url;
        const isMainCategory = cat.url.includes('/categories/') && 
                              !cat.url.endsWith('/categories') &&
                              cat.name.length > 2 &&
                              (cat.url.match(/\/categories\/[^\/]+$/));
        return hasName && isMainCategory;
      });
    });

    const categories = [];
    
    for (const mainCategoryLink of mainCategoryLinks) {
      try {
        console.log(`📂 Обрабатываем категорию: ${mainCategoryLink.name}`);
        
        await this.page.goto(mainCategoryLink.url, { 
          waitUntil: 'domcontentloaded',
          timeout: 60000 
        });
        await this.page.waitForTimeout(5000);
        
        const subcategoryLinks = await this.page.$$eval('.category-tile__link, a.cardTile', links => {
          return links.map(link => ({
            name: link.textContent?.trim() || '',
            url: (link as HTMLAnchorElement).href
          })).filter(subcat => {
            const hasName = subcat.name && subcat.url;
            const isSubcategory = subcat.url.includes('/categories/') && 
                                 subcat.name.length > 2;
            return hasName && isSubcategory;
          });
        });
        
        console.log(`   📁 Найдено ${subcategoryLinks.length} подкатегорий`);
        
        categories.push({
          name: mainCategoryLink.name,
          url: mainCategoryLink.url,
          subcategories: subcategoryLinks.map(sub => ({
            name: sub.name,
            url: sub.url,
            images: [],
            products: [],
            totalPages: 0
          }))
        });
        
      } catch (error) {
        console.error(`❌ Ошибка обработки категории ${mainCategoryLink.name}:`, error);
      }
    }

    return categories;
  }

  private async processCategory(category: {name: string, url: string, subcategories: SubcategoryInfo[]}): Promise<void> {
    console.log(`\n🔄 Обрабатываем категорию: ${category.name}`);
    
    for (const subcategory of category.subcategories) {
      await this.processSubcategory(category, subcategory);
    }
    
    // Сохраняем данные категории
    await this.saveCategoryData(category);
    console.log(`✅ Категория ${category.name} завершена (${category.subcategories.reduce((sum, sub) => sum + sub.products.length, 0)} продуктов)`);
  }

  private async processSubcategory(category: any, subcategory: SubcategoryInfo): Promise<void> {
    if (!this.page) throw new Error('Browser not initialized');
    
    console.log(`   📁 Обрабатываем подкатегорию: ${subcategory.name}`);
    
    try {
      await this.page.goto(subcategory.url, { 
        waitUntil: 'domcontentloaded',
        timeout: 60000 
      });
      
      await this.page.waitForTimeout(8000);
      
      // Получаем информацию о подкатегории
      const subcategoryInfo = await this.page.evaluate(() => {
        const images: string[] = [];
        const imgElements = document.querySelectorAll('img');
        imgElements.forEach(img => {
          if (img.src && !img.src.includes('data:') && !img.src.includes('logo')) {
            images.push(img.src);
          }
        });
        
        return {
          description: document.querySelector('meta[name="description"]')?.getAttribute('content') || '',
          images: images.slice(0, 10)
        };
      });
      
      subcategory.description = subcategoryInfo.description;
      subcategory.images = subcategoryInfo.images;
      
      // Скрапим все продукты со всех страниц
      await this.scrapeAllProductsFromSubcategory(subcategory);
      
      subcategory.scrapedAt = new Date().toISOString();
      
      console.log(`      ✅ ${subcategory.name}: ${subcategory.products.length} продуктов с ${subcategory.totalPages} страниц`);
      
    } catch (error) {
      console.error(`      ❌ Ошибка обработки подкатегории ${subcategory.name}:`, error);
    }
  }

  private async scrapeAllProductsFromSubcategory(subcategory: SubcategoryInfo): Promise<void> {
    if (!this.page) throw new Error('Browser not initialized');
    
    let currentPage = 1;
    let hasNextPage = true;
    const productsPerPage = 26;
    
    while (hasNextPage && currentPage <= 50) { // Ограничиваем до 50 страниц
      const skip = (currentPage - 1) * productsPerPage;
      const pageUrl = skip > 0 ? `${subcategory.url}?skip=${skip}` : subcategory.url;
      
      try {
        await this.page.goto(pageUrl, { 
          waitUntil: 'domcontentloaded',
          timeout: 30000 
        });
        
        await this.page.waitForTimeout(8000);
        
        // Прокрутка для загрузки контента
        await this.page.evaluate(() => {
          window.scrollTo(0, document.body.scrollHeight);
        });
        await this.page.waitForTimeout(3000);
        
        await this.page.evaluate(() => {
          window.scrollTo(0, 0);
        });
        await this.page.waitForTimeout(2000);
        
        const productUrls = await this.page.$$eval('a[href*="product"]', links => {
          return Array.from(new Set(
            links.map(link => (link as HTMLAnchorElement).href)
              .filter(url => url.includes('/products/'))
          ));
        });
        
        if (productUrls.length === 0) {
          hasNextPage = false;
          break;
        }
        
        console.log(`      📄 Страница ${currentPage}: ${productUrls.length} продуктов`);
        
        // Скрапим каждый продукт
        for (const productUrl of productUrls) {
          try {
            const product = await this.scrapeProduct(productUrl, subcategory.name);
            if (product) {
              product.category = subcategory.name;
              subcategory.products.push(product);
            }
            await this.delay(1000);
          } catch (error) {
            console.error(`         ❌ Ошибка скрапинга продукта ${productUrl}:`, error);
          }
        }
        
        // Проверяем пагинацию
        const paginationInfo = await this.page.evaluate(() => {
          const pager = document.querySelector('.k-pager');
          if (!pager) return { hasNext: false, totalPages: 1 };
          
          const ariaLabel = pager.getAttribute('aria-label') || '';
          const match = ariaLabel.match(/page (\d+) of (\d+)/);
          
          if (match) {
            const currentPageNum = parseInt(match[1]);
            const totalPages = parseInt(match[2]);
            return {
              hasNext: currentPageNum < totalPages,
              totalPages,
              currentPageNum
            };
          }
          
          return { hasNext: false, totalPages: 1 };
        });
        
        if (paginationInfo.hasNext && currentPage < paginationInfo.totalPages) {
          currentPage++;
        } else {
          hasNextPage = false;
        }
        
        subcategory.totalPages = paginationInfo.totalPages || currentPage;
        
      } catch (error) {
        console.error(`      ❌ Ошибка страницы ${currentPage}:`, error);
        hasNextPage = false;
      }
    }
  }

  private async scrapeProduct(productUrl: string, subcategoryName: string): Promise<ProductInfo | null> {
    if (!this.page) throw new Error('Browser not initialized');

    try {
      await this.page.goto(productUrl, {
        waitUntil: 'domcontentloaded',
        timeout: 30000
      });

      await this.page.waitForTimeout(3000);

      const productData = await this.page.evaluate(() => {
        const fstItemNo = window.location.pathname.split('/').pop() || '';
        const title = document.querySelector('h1')?.textContent?.trim() || document.title;
        const description = document.querySelector('meta[name="description"]')?.getAttribute('content') || '';

        const specifications: Record<string, string> = {};
        const dimensions: Record<string, string> = {};

        // Извлекаем характеристики из таблицы
        const detailRows = document.querySelectorAll('.product-details-main__table__row');
        detailRows.forEach(row => {
          const label = row.querySelector('.product-details-main__table__row__label')?.textContent?.trim() || '';
          const value = row.querySelector('.product-details-main__table__row__value')?.textContent?.trim() || '';

          if (label && value && value !== '-') {
            specifications[label] = value;
          }
        });

        // Извлекаем размеры
        const dimensionElements = document.querySelectorAll('.dimensions-container__dimensions-details > div');
        dimensionElements.forEach(dimEl => {
          const text = dimEl.textContent?.trim() || '';
          const lines = text.split('\n').map(line => line.trim()).filter(line => line);

          if (lines.length >= 2) {
            const key = lines[0];
            const value = lines[1];
            if (key && value) {
              dimensions[key] = value;
            }
          }
        });

        // Извлекаем изображения
        const images: string[] = [];
        const imgElements = document.querySelectorAll('img');
        imgElements.forEach(img => {
          if (img.src && !img.src.includes('data:') && !img.src.includes('icon') && !img.src.includes('logo')) {
            images.push(img.src);
          }
        });

        return {
          fstItemNo,
          title,
          description,
          specifications,
          dimensions,
          images: Array.from(new Set(images)),
          url: window.location.href
        };
      });

      // Извлекаем дополнительные поля
      const additionalFields = await this.page.evaluate(() => {
        const getFieldFromTable = (label: string): string => {
          const rows = document.querySelectorAll('.product-details-main__table__row');
          for (const row of rows) {
            const labelEl = row.querySelector('.product-details-main__table__row__label');
            const valueEl = row.querySelector('.product-details-main__table__row__value');

            if (labelEl && valueEl) {
              const labelText = labelEl.textContent?.trim().toLowerCase() || '';
              const valueText = valueEl.textContent?.trim() || '';

              if (labelText.includes(label.toLowerCase()) && valueText && valueText !== '-') {
                return valueText;
              }
            }
          }
          return '';
        };

        return {
          legacyItemNo: getFieldFromTable('legacy item no'),
          successorItem: getFieldFromTable('successor item'),
          variant: getFieldFromTable('variant'),
          netWeight: getFieldFromTable('net weight'),
          packaging: getFieldFromTable('packaging'),
          reach: getFieldFromTable('reach'),
          rohs: getFieldFromTable('rohs'),
          brand: getFieldFromTable('brand'),
          material: getFieldFromTable('material')
        };
      });

      const product: ProductInfo = {
        fstItemNo: productData.fstItemNo,
        title: productData.title,
        description: productData.description,
        specifications: productData.specifications,
        dimensions: productData.dimensions,
        images: productData.images,
        category: '',
        subcategory: subcategoryName,
        url: productData.url,
        scrapedAt: new Date().toISOString(),
        ...additionalFields
      };

      return product;

    } catch (error) {
      console.error(`❌ Ошибка скрапинга продукта ${productUrl}:`, error);
      return null;
    }
  }

  private async saveCategoryData(category: any): Promise<void> {
    const fileName = `${category.name.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase()}.json`;
    const filePath = path.join(this.outputDir, fileName);

    const categoryData = {
      category: category.name,
      url: category.url,
      totalProducts: category.subcategories.reduce((sum: number, sub: any) => sum + sub.products.length, 0),
      scrapedAt: new Date().toISOString(),
      subcategories: category.subcategories.map((sub: any) => ({
        name: sub.name,
        url: sub.url,
        description: sub.description,
        images: sub.images,
        totalPages: sub.totalPages,
        totalProducts: sub.products.length,
        products: sub.products
      }))
    };

    await fs.writeJSON(filePath, categoryData, { spaces: 2 });
    console.log(`💾 Сохранено: ${fileName}`);
  }

  private async delay(ms: number = 2000): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Запуск скрапера
async function main() {
  const scraper = new FinalFSTScraper('./data');
  await scraper.start();
}

main().catch(console.error);
