import { chromium } from 'playwright';

async function testProductPage() {
  console.log('🔍 Анализ страницы продукта для извлечения характеристик');
  console.log('====================================================\n');

  let browser;
  let page;

  try {
    browser = await chromium.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-dev-shm-usage']
    });

    page = await browser.newPage({
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    });
    
    await page.setViewportSize({ width: 1920, height: 1080 });
    page.setDefaultTimeout(60000);

    // Тестируем конкретный продукт
    const productUrl = 'https://products.fst.com/global/en/categories/radial-shaft-seal-simmerring-b2/products/2528';
    
    console.log(`🌐 Переходим на страницу продукта: ${productUrl}`);
    await page.goto(productUrl, { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });
    
    await page.waitForTimeout(5000);

    console.log('1️⃣ Анализ основной информации...');
    
    const basicInfo = await page.evaluate(() => {
      return {
        title: document.title,
        h1: document.querySelector('h1')?.textContent?.trim() || '',
        url: window.location.href,
        fstItemNo: window.location.pathname.split('/').pop() || '',
        metaDescription: document.querySelector('meta[name="description"]')?.getAttribute('content') || ''
      };
    });

    console.log(`📄 Заголовок: ${basicInfo.title}`);
    console.log(`📄 H1: ${basicInfo.h1}`);
    console.log(`🔢 FST Item No: ${basicInfo.fstItemNo}`);
    console.log(`📝 Описание: ${basicInfo.metaDescription.substring(0, 100)}...`);

    console.log('\n2️⃣ Поиск таблиц и списков характеристик...');
    
    const tablesAnalysis = await page.evaluate(() => {
      const analysis = {
        tables: [] as Array<{selector: string, rows: number, content: string}>,
        lists: [] as Array<{selector: string, items: number, content: string}>,
        divs: [] as Array<{selector: string, content: string}>
      };
      
      // Анализ таблиц
      const tables = document.querySelectorAll('table');
      tables.forEach((table, i) => {
        const rows = table.querySelectorAll('tr').length;
        const content = table.textContent?.trim().substring(0, 200) || '';
        analysis.tables.push({
          selector: `table:nth-child(${i + 1})`,
          rows,
          content
        });
      });
      
      // Анализ списков
      const lists = document.querySelectorAll('ul, ol, dl');
      lists.forEach((list, i) => {
        const items = list.querySelectorAll('li, dt, dd').length;
        const content = list.textContent?.trim().substring(0, 200) || '';
        if (content.length > 10) {
          analysis.lists.push({
            selector: `${list.tagName.toLowerCase()}:nth-child(${i + 1})`,
            items,
            content
          });
        }
      });
      
      // Анализ div-ов с потенциальными характеристиками
      const divs = document.querySelectorAll('div');
      divs.forEach(div => {
        const text = div.textContent?.trim() || '';
        if (text.includes('FST item no') || 
            text.includes('Legacy item') || 
            text.includes('Material') || 
            text.includes('diameter') ||
            text.includes('Brand') ||
            text.includes('Weight') ||
            text.includes('Packaging')) {
          analysis.divs.push({
            selector: div.className ? `.${div.className.split(' ')[0]}` : 'div',
            content: text.substring(0, 300)
          });
        }
      });
      
      return analysis;
    });

    console.log(`📊 Найдено таблиц: ${tablesAnalysis.tables.length}`);
    tablesAnalysis.tables.forEach((table, i) => {
      console.log(`   ${i + 1}. ${table.selector} (${table.rows} строк)`);
      console.log(`      ${table.content.substring(0, 100)}...`);
    });

    console.log(`\n📊 Найдено списков: ${tablesAnalysis.lists.length}`);
    tablesAnalysis.lists.forEach((list, i) => {
      console.log(`   ${i + 1}. ${list.selector} (${list.items} элементов)`);
      console.log(`      ${list.content.substring(0, 100)}...`);
    });

    console.log(`\n📊 Найдено div-ов с характеристиками: ${tablesAnalysis.divs.length}`);
    tablesAnalysis.divs.forEach((div, i) => {
      console.log(`   ${i + 1}. ${div.selector}`);
      console.log(`      ${div.content.substring(0, 150)}...`);
    });

    console.log('\n3️⃣ Поиск конкретных полей...');
    
    const specificFields = await page.evaluate(() => {
      const fields: Record<string, string> = {};
      
      // Поиск по тексту
      const searchTerms = [
        'FST item no',
        'Legacy item no',
        'Successor item',
        'Variant',
        'Net weight',
        'Packaging',
        'REACH',
        'ROHS',
        'Brand',
        'Material',
        'Inner diameter',
        'Outer diameter',
        'Seal width',
        'Spring',
        'Material spring'
      ];
      
      searchTerms.forEach(term => {
        // Ищем элементы, содержащие этот термин
        const elements = Array.from(document.querySelectorAll('*')).filter(el => {
          const text = el.textContent || '';
          return text.toLowerCase().includes(term.toLowerCase()) && 
                 text.length < 500; // Исключаем слишком длинные элементы
        });
        
        elements.forEach(el => {
          const text = el.textContent?.trim() || '';
          const lines = text.split('\n').map(line => line.trim()).filter(line => line);
          
          // Ищем строку с термином и следующую строку со значением
          for (let i = 0; i < lines.length - 1; i++) {
            if (lines[i].toLowerCase().includes(term.toLowerCase())) {
              const value = lines[i + 1];
              if (value && value.length < 100 && !value.toLowerCase().includes(term.toLowerCase())) {
                fields[term] = value;
                break;
              }
            }
          }
          
          // Также ищем в том же элементе после двоеточия
          if (text.includes(':')) {
            const parts = text.split(':');
            if (parts.length === 2 && parts[0].toLowerCase().includes(term.toLowerCase())) {
              fields[term] = parts[1].trim();
            }
          }
        });
      });
      
      return fields;
    });

    console.log('📋 Найденные поля:');
    Object.entries(specificFields).forEach(([key, value]) => {
      console.log(`   ${key}: ${value}`);
    });

    console.log('\n4️⃣ Анализ изображений...');
    
    const images = await page.evaluate(() => {
      const imgs: string[] = [];
      const imgElements = document.querySelectorAll('img');
      imgElements.forEach(img => {
        if (img.src && !img.src.includes('data:') && !img.src.includes('icon')) {
          imgs.push(img.src);
        }
      });
      return Array.from(new Set(imgs));
    });

    console.log(`🖼️ Найдено изображений: ${images.length}`);
    images.slice(0, 5).forEach((img, i) => {
      console.log(`   ${i + 1}. ${img}`);
    });

    console.log('\n5️⃣ Поиск структурированных данных...');
    
    const structuredData = await page.evaluate(() => {
      const data = {
        jsonLd: [] as any[],
        microdata: [] as any[],
        dataAttributes: [] as string[]
      };
      
      // JSON-LD
      const jsonLdScripts = document.querySelectorAll('script[type="application/ld+json"]');
      jsonLdScripts.forEach(script => {
        try {
          const json = JSON.parse(script.textContent || '');
          data.jsonLd.push(json);
        } catch (e) {
          // Ignore
        }
      });
      
      // Data attributes
      const elementsWithData = document.querySelectorAll('[data-*]');
      elementsWithData.forEach(el => {
        Array.from(el.attributes).forEach(attr => {
          if (attr.name.startsWith('data-') && attr.value) {
            data.dataAttributes.push(`${attr.name}="${attr.value}"`);
          }
        });
      });
      
      return data;
    });

    console.log(`📊 JSON-LD схем: ${structuredData.jsonLd.length}`);
    if (structuredData.jsonLd.length > 0) {
      console.log('   Примеры:');
      structuredData.jsonLd.slice(0, 2).forEach((schema, i) => {
        console.log(`   ${i + 1}. ${JSON.stringify(schema).substring(0, 100)}...`);
      });
    }

    console.log(`📊 Data-атрибутов: ${structuredData.dataAttributes.length}`);
    if (structuredData.dataAttributes.length > 0) {
      console.log('   Примеры:');
      structuredData.dataAttributes.slice(0, 5).forEach((attr, i) => {
        console.log(`   ${i + 1}. ${attr}`);
      });
    }

  } catch (error) {
    console.log(`💥 Ошибка: ${error}`);
  } finally {
    if (browser) {
      await browser.close();
    }
  }

  console.log('\n🏁 Анализ завершен');
}

testProductPage().catch(console.error);
