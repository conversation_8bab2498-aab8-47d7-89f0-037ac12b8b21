import { chromium } from 'playwright';

async function testBrowserFix() {
  console.log('🔧 Тестирование исправления браузера');
  console.log('===================================\n');

  let browser;
  let page;

  try {
    console.log('1️⃣ Попытка запуска браузера с упрощенными настройками...');
    
    const maxRetries = 3;
    let attempt = 0;
    
    while (attempt < maxRetries) {
      try {
        browser = await chromium.launch({ 
          headless: true,
          args: [
            '--no-sandbox', 
            '--disable-dev-shm-usage'
          ]
        });
        
        console.log(`✅ Браузер запущен успешно (попытка ${attempt + 1})`);
        break;
        
      } catch (error) {
        attempt++;
        console.log(`❌ Ошибка запуска браузера, попытка ${attempt}/${maxRetries}: ${error}`);
        
        if (browser) {
          try {
            await browser.close();
          } catch (e) {
            // Игнорируем ошибки закрытия
          }
          browser = undefined;
        }
        
        if (attempt >= maxRetries) {
          throw new Error(`Не удалось запустить браузер после ${maxRetries} попыток`);
        }
        
        // Ждем перед повторной попыткой
        console.log(`⏳ Ожидание ${2000 * attempt}мс перед повторной попыткой...`);
        await new Promise(resolve => setTimeout(resolve, 2000 * attempt));
      }
    }

    console.log('2️⃣ Создание страницы...');
    page = await browser.newPage({
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    });
    
    await page.setViewportSize({ width: 1920, height: 1080 });
    page.setDefaultTimeout(60000);
    page.setDefaultNavigationTimeout(60000);
    
    console.log('✅ Страница создана успешно');

    console.log('3️⃣ Тестирование загрузки страницы...');
    const testUrl = 'https://products.fst.com/global/en';
    
    await page.goto(testUrl, { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });
    
    console.log('✅ Страница загружена успешно');

    console.log('4️⃣ Проверка базовой функциональности...');
    const pageInfo = await page.evaluate(() => {
      return {
        title: document.title,
        url: window.location.href,
        bodyLength: document.body?.textContent?.length || 0
      };
    });
    
    console.log(`📄 Заголовок: ${pageInfo.title}`);
    console.log(`🔗 URL: ${pageInfo.url}`);
    console.log(`📊 Размер контента: ${pageInfo.bodyLength} символов`);

    console.log('5️⃣ Тестирование перехода на категорию...');
    const categoryUrl = 'https://products.fst.com/global/en/categories/rotary-seals';
    
    await page.goto(categoryUrl, { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });
    
    await page.waitForTimeout(5000);
    
    const categoryInfo = await page.evaluate(() => {
      return {
        title: document.title,
        linksCount: document.querySelectorAll('a').length,
        categoryLinksCount: document.querySelectorAll('a[href*="/categories/"]').length,
        subcategoryLinksCount: document.querySelectorAll('.category-tile__link, a.cardTile').length
      };
    });
    
    console.log(`📄 Заголовок категории: ${categoryInfo.title}`);
    console.log(`🔗 Всего ссылок: ${categoryInfo.linksCount}`);
    console.log(`📂 Ссылок на категории: ${categoryInfo.categoryLinksCount}`);
    console.log(`📁 Ссылок на подкатегории: ${categoryInfo.subcategoryLinksCount}`);

    if (categoryInfo.subcategoryLinksCount > 0) {
      console.log('✅ Подкатегории найдены! Браузер работает корректно.');
      
      // Получаем список подкатегорий
      const subcategories = await page.evaluate(() => {
        const links = document.querySelectorAll('.category-tile__link, a.cardTile');
        return Array.from(links).slice(0, 3).map(link => ({
          name: (link.textContent || '').trim().substring(0, 50),
          url: (link as HTMLAnchorElement).href
        })).filter(sub => sub.name && sub.url);
      });
      
      console.log('\n📋 Найденные подкатегории:');
      subcategories.forEach((sub, i) => {
        console.log(`   ${i + 1}. ${sub.name}`);
        console.log(`      → ${sub.url}`);
      });
      
    } else {
      console.log('⚠️ Подкатегории не найдены. Возможно, требуется больше времени ожидания.');
    }

    console.log('\n🎉 ТЕСТ УСПЕШНО ЗАВЕРШЕН!');
    console.log('Браузер работает стабильно с упрощенными настройками.');

  } catch (error) {
    console.log(`💥 Ошибка теста: ${error}`);
  } finally {
    if (browser) {
      try {
        await browser.close();
        console.log('🔒 Браузер закрыт');
      } catch (error) {
        console.log(`⚠️ Ошибка при закрытии браузера: ${error}`);
      }
    }
  }

  console.log('\n🏁 Тест завершен');
}

testBrowserFix().catch(console.error);
