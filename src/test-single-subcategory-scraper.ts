import { FSTScraperNew } from './new-scraper';
import * as path from 'path';
import * as fs from 'fs-extra';

async function testSingleSubcategoryScraper() {
  console.log('🧪 Тест скрапера одной подкатегории с пагинацией');
  console.log('===============================================\n');

  const outputDir = path.join(__dirname, '..', 'data-test');
  const logFile = path.join(__dirname, '..', 'scrape-log-test.json');
  
  // Очищаем тестовую папку
  await fs.remove(outputDir);
  await fs.ensureDir(outputDir);
  
  const scraper = new FSTScraperNew({
    baseUrl: 'https://products.fst.com/global/en',
    outputDir,
    logFile,
    delay: 1500
  });
  
  try {
    console.log('🚀 Запуск тестирования...');
    console.log('Особенности:');
    console.log('- Тестируем только одну подкатегорию');
    console.log('- Используем URL пагинацию (?skip=N)');
    console.log('- Ограничиваем до 3 страниц для скорости');
    console.log('- Собираем полную информацию о продуктах\n');
    
    // Создаем тестовый метод для одной подкатегории
    const testSingleSubcategory = async () => {
      // Инициализируем браузер
      await (scraper as any).initBrowser();
      
      try {
        // Создаем тестовую подкатегорию
        const testSubcategory = {
          name: 'Simmerring® (Test)',
          url: 'https://products.fst.com/global/en/categories/rotary-seals/radial-shaft-seal-simmerring',
          description: '',
          images: [],
          products: [],
          totalPages: 0
        };
        
        console.log(`📂 Обрабатываем подкатегорию: ${testSubcategory.name}`);
        console.log(`🔗 URL: ${testSubcategory.url}`);
        
        // Вызываем метод обработки подкатегории
        await (scraper as any).processSubcategory(
          { name: 'Rotary Seals', totalProducts: 0 }, 
          testSubcategory
        );
        
        return testSubcategory;
        
      } finally {
        // Закрываем браузер
        await (scraper as any).closeBrowser();
      }
    };
    
    const result = await testSingleSubcategory();
    
    console.log('\n📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:');
    console.log('============================');
    console.log(`📂 Подкатегория: ${result.name}`);
    console.log(`📄 Описание: ${result.description || 'Не найдено'}`);
    console.log(`🖼️ Изображений: ${result.images.length}`);
    console.log(`📦 Продуктов: ${result.products.length}`);
    console.log(`📄 Страниц: ${result.totalPages}`);
    console.log(`⏰ Время обработки: ${result.scrapedAt}`);
    
    if (result.images.length > 0) {
      console.log('\n🖼️ Примеры изображений:');
      result.images.slice(0, 3).forEach((img, i) => {
        console.log(`   ${i + 1}. ${img}`);
      });
    }
    
    if (result.products.length > 0) {
      console.log('\n📦 Примеры продуктов:');
      result.products.slice(0, 5).forEach((product, i) => {
        console.log(`\n   ${i + 1}. ${product.title || product.fstItemNo}`);
        console.log(`      FST Item No: ${product.fstItemNo}`);
        console.log(`      URL: ${product.url}`);
        console.log(`      Характеристик: ${Object.keys(product.specifications).length}`);
        console.log(`      Размеров: ${Object.keys(product.dimensions).length}`);
        console.log(`      Изображений: ${product.images.length}`);
        
        if (product.legacyItemNo) console.log(`      Legacy Item No: ${product.legacyItemNo}`);
        if (product.variant) console.log(`      Variant: ${product.variant}`);
        if (product.material) console.log(`      Material: ${product.material}`);
        if (product.brand) console.log(`      Brand: ${product.brand}`);
        
        // Показываем несколько характеристик
        const specKeys = Object.keys(product.specifications);
        if (specKeys.length > 0) {
          console.log(`      Характеристики:`);
          specKeys.slice(0, 3).forEach(key => {
            console.log(`        ${key}: ${product.specifications[key]}`);
          });
        }
        
        // Показываем размеры
        const dimKeys = Object.keys(product.dimensions);
        if (dimKeys.length > 0) {
          console.log(`      Размеры:`);
          dimKeys.slice(0, 3).forEach(key => {
            console.log(`        ${key}: ${product.dimensions[key]}`);
          });
        }
      });
      
      console.log('\n📊 Статистика по продуктам:');
      const stats = {
        withLegacyItemNo: result.products.filter(p => p.legacyItemNo).length,
        withVariant: result.products.filter(p => p.variant).length,
        withMaterial: result.products.filter(p => p.material).length,
        withBrand: result.products.filter(p => p.brand).length,
        withSpecifications: result.products.filter(p => Object.keys(p.specifications).length > 0).length,
        withDimensions: result.products.filter(p => Object.keys(p.dimensions).length > 0).length,
        withImages: result.products.filter(p => p.images.length > 0).length
      };
      
      console.log(`   Legacy Item No: ${stats.withLegacyItemNo}/${result.products.length}`);
      console.log(`   Variant: ${stats.withVariant}/${result.products.length}`);
      console.log(`   Material: ${stats.withMaterial}/${result.products.length}`);
      console.log(`   Brand: ${stats.withBrand}/${result.products.length}`);
      console.log(`   Характеристики: ${stats.withSpecifications}/${result.products.length}`);
      console.log(`   Размеры: ${stats.withDimensions}/${result.products.length}`);
      console.log(`   Изображения: ${stats.withImages}/${result.products.length}`);
    }
    
    // Сохраняем результат в файл
    const testResultFile = path.join(outputDir, 'test-result.json');
    await fs.writeJSON(testResultFile, {
      subcategory: result.name,
      url: result.url,
      description: result.description,
      totalImages: result.images.length,
      totalProducts: result.products.length,
      totalPages: result.totalPages,
      scrapedAt: result.scrapedAt,
      sampleProducts: result.products.slice(0, 3),
      statistics: {
        withLegacyItemNo: result.products.filter(p => p.legacyItemNo).length,
        withVariant: result.products.filter(p => p.variant).length,
        withMaterial: result.products.filter(p => p.material).length,
        withBrand: result.products.filter(p => p.brand).length,
        withSpecifications: result.products.filter(p => Object.keys(p.specifications).length > 0).length,
        withDimensions: result.products.filter(p => Object.keys(p.dimensions).length > 0).length,
        withImages: result.products.filter(p => p.images.length > 0).length
      }
    }, { spaces: 2 });
    
    console.log(`\n💾 Результат сохранен в: ${testResultFile}`);
    
    if (result.products.length > 0) {
      console.log('\n🎉 ТЕСТ УСПЕШЕН!');
      console.log('✅ Пагинация работает');
      console.log('✅ Продукты скрапятся');
      console.log('✅ Характеристики извлекаются');
      console.log('✅ Изображения собираются');
      console.log('✅ Данные сохраняются');
    } else {
      console.log('\n❌ ТЕСТ НЕ ПРОШЕЛ');
      console.log('Продукты не найдены');
    }
    
  } catch (error) {
    console.error('💥 Ошибка тестирования:', error);
  }
  
  console.log('\n🏁 Тестирование завершено');
}

testSingleSubcategoryScraper().catch(console.error);
