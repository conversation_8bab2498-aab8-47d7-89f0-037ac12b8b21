import { FSTScraperNew } from './new-scraper';
import * as path from 'path';

async function testNewScraper() {
  console.log('🧪 Тестирование нового скрапера с пагинацией');
  console.log('============================================\n');

  const outputDir = path.join(__dirname, '..', 'data-new');
  const logFile = path.join(__dirname, '..', 'scrape-log-new.json');
  
  const scraper = new FSTScraperNew({
    baseUrl: 'https://products.fst.com/global/en',
    outputDir,
    logFile,
    delay: 2000
  });
  
  try {
    console.log('🚀 Запуск нового скрапера...');
    console.log('Особенности:');
    console.log('- Обрабатывает пагинацию (все страницы)');
    console.log('- Сохраняет по категориям отдельно');
    console.log('- Собирает все характеристики и изображения');
    console.log('- Структура: Категория → Подкатегории → Продукты\n');
    
    await scraper.start();
    
    console.log('\n🎉 Тестирование завершено успешно!');
    console.log(`📁 Данные сохранены в: ${outputDir}`);
    console.log(`📋 Лог процесса: ${logFile}`);
    
  } catch (error) {
    console.error('💥 Ошибка тестирования:', error);
  }
}

testNewScraper().catch(console.error);
