import { chromium, <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';

async function runProductApiDiagnostics() {
  console.log('🔍 FST Scraper - Диагностика API продуктов');
  console.log('==========================================\n');

  let browser: Browser | undefined;
  let page: Page | undefined;

  try {
    browser = await chromium.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-dev-shm-usage']
    });

    page = await browser.newPage({
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    });

    const problemUrl = 'https://products.fst.com/global/en/categories/rotary-seals';
    
    console.log('1️⃣ Перехват всех сетевых запросов...');
    
    const allRequests: Array<{
      url: string,
      method: string,
      resourceType: string,
      status?: number,
      responseBody?: string,
      requestBody?: string,
      headers?: Record<string, string>
    }> = [];
    
    // Перехватываем все запросы
    page.on('request', (request) => {
      const headers: Record<string, string> = {};
      Object.entries(request.headers()).forEach(([key, value]) => {
        headers[key] = value;
      });
      
      allRequests.push({
        url: request.url(),
        method: request.method(),
        resourceType: request.resourceType(),
        requestBody: request.postData() || undefined,
        headers
      });
    });
    
    page.on('response', async (response) => {
      const request = allRequests.find(req => req.url === response.url());
      if (request) {
        request.status = response.status();
        
        // Сохраняем тело ответа для потенциальных API запросов
        if (response.request().resourceType() === 'xhr' || 
            response.request().resourceType() === 'fetch' ||
            response.url().includes('api') ||
            response.url().includes('product') ||
            response.url().includes('catalog') ||
            response.url().includes('search') ||
            response.url().includes('filter') ||
            response.url().includes('category')) {
          try {
            const text = await response.text();
            request.responseBody = text;
          } catch (e) {
            request.responseBody = 'Failed to read response body';
          }
        }
      }
    });

    console.log('🌐 Загружаем страницу категории...');
    await page.goto(problemUrl, { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });

    console.log('⏳ Ожидание 15 секунд для всех запросов...');
    await page.waitForTimeout(15000);

    // Попробуем прокрутить страницу для триггера ленивой загрузки
    console.log('📜 Прокрутка страницы для триггера ленивой загрузки...');
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight);
    });
    await page.waitForTimeout(3000);
    
    await page.evaluate(() => {
      window.scrollTo(0, 0);
    });
    await page.waitForTimeout(3000);

    // Анализ всех запросов
    console.log('\n2️⃣ Анализ всех запросов:');
    console.log(`📊 Всего запросов: ${allRequests.length}`);
    
    // Группируем запросы по типам
    const requestsByType = {
      api: allRequests.filter(req => 
        req.url.includes('api') || 
        req.resourceType === 'xhr' || 
        req.resourceType === 'fetch'
      ),
      product: allRequests.filter(req => 
        req.url.toLowerCase().includes('product') ||
        req.url.toLowerCase().includes('catalog') ||
        req.url.toLowerCase().includes('search') ||
        req.url.toLowerCase().includes('filter')
      ),
      cms: allRequests.filter(req => 
        req.url.includes('cms') ||
        req.url.includes('strapi') ||
        req.url.includes('contentful')
      ),
      graphql: allRequests.filter(req => 
        req.url.includes('graphql') ||
        req.requestBody?.includes('query') ||
        req.requestBody?.includes('mutation')
      )
    };

    console.log(`📊 API запросы: ${requestsByType.api.length}`);
    console.log(`📊 Запросы продуктов: ${requestsByType.product.length}`);
    console.log(`📊 CMS запросы: ${requestsByType.cms.length}`);
    console.log(`📊 GraphQL запросы: ${requestsByType.graphql.length}`);

    // Детальный анализ API запросов
    if (requestsByType.api.length > 0) {
      console.log('\n3️⃣ Детальный анализ API запросов:');
      requestsByType.api.forEach((req, i) => {
        const status = req.status ? (req.status >= 200 && req.status < 300 ? '✅' : '❌') : '⏳';
        console.log(`\n   ${i + 1}. ${status} ${req.method} ${req.url}`);
        
        if (req.requestBody) {
          console.log(`      Request Body: ${req.requestBody.substring(0, 200)}...`);
        }
        
        if (req.responseBody && req.responseBody.length > 0) {
          if (req.responseBody.length < 500) {
            console.log(`      Response: ${req.responseBody}`);
          } else {
            console.log(`      Response: ${req.responseBody.length} characters`);
            // Попробуем найти JSON с продуктами
            try {
              const json = JSON.parse(req.responseBody);
              if (Array.isArray(json)) {
                console.log(`      JSON Array with ${json.length} items`);
              } else if (json.data && Array.isArray(json.data)) {
                console.log(`      JSON with data array: ${json.data.length} items`);
              } else if (json.products && Array.isArray(json.products)) {
                console.log(`      JSON with products array: ${json.products.length} items`);
              } else {
                console.log(`      JSON object with keys: ${Object.keys(json).join(', ')}`);
              }
            } catch (e) {
              console.log(`      Non-JSON response`);
            }
          }
        }
      });
    }

    // Поиск скрытых API endpoints
    console.log('\n4️⃣ Поиск скрытых API endpoints в JavaScript...');
    
    const jsEndpoints = await page.evaluate(() => {
      const endpoints: string[] = [];
      const scripts = document.querySelectorAll('script');
      
      scripts.forEach(script => {
        const content = script.textContent || '';
        
        // Поиск URL паттернов
        const urlPatterns = [
          /https?:\/\/[^"'\s]+api[^"'\s]*/gi,
          /\/api\/[^"'\s]*/gi,
          /["']\/[^"']*product[^"']*["']/gi,
          /["']\/[^"']*catalog[^"']*["']/gi,
          /["']\/[^"']*search[^"']*["']/gi,
          /baseURL[^"']*["'][^"']+["']/gi
        ];
        
        urlPatterns.forEach(pattern => {
          const matches = content.match(pattern);
          if (matches) {
            matches.forEach(match => {
              const cleaned = match.replace(/['"]/g, '');
              if (!endpoints.includes(cleaned) && cleaned.length > 5) {
                endpoints.push(cleaned);
              }
            });
          }
        });
      });
      
      return endpoints.slice(0, 20); // Ограничиваем до 20 результатов
    });
    
    console.log(`   Найдено ${jsEndpoints.length} потенциальных endpoints:`);
    jsEndpoints.forEach((endpoint, i) => {
      console.log(`   ${i + 1}. ${endpoint}`);
    });

    // Попробуем найти конфигурацию Nuxt
    console.log('\n5️⃣ Анализ конфигурации Nuxt.js...');
    
    const nuxtConfig = await page.evaluate(() => {
      const config = {
        hasNuxt: typeof (window as any).$nuxt !== 'undefined',
        nuxtVersion: '',
        apiBase: '',
        publicConfig: {},
        runtimeConfig: {}
      };
      
      try {
        if ((window as any).$nuxt) {
          const nuxt = (window as any).$nuxt;
          config.nuxtVersion = nuxt.$config?.nuxtVersion || 'unknown';
          config.apiBase = nuxt.$config?.apiBase || nuxt.$config?.baseURL || '';
          config.publicConfig = nuxt.$config?.public || {};
          config.runtimeConfig = nuxt.$config || {};
        }
      } catch (e) {
        // Ignore errors
      }
      
      return config;
    });
    
    console.log(`   Nuxt.js: ${nuxtConfig.hasNuxt ? '✅' : '❌'}`);
    console.log(`   Version: ${nuxtConfig.nuxtVersion}`);
    console.log(`   API Base: ${nuxtConfig.apiBase}`);
    console.log(`   Public Config Keys: ${Object.keys(nuxtConfig.publicConfig).join(', ')}`);
    console.log(`   Runtime Config Keys: ${Object.keys(nuxtConfig.runtimeConfig).join(', ')}`);

    // Попробуем вызвать потенциальные API endpoints
    console.log('\n6️⃣ Тестирование потенциальных API endpoints...');
    
    const testEndpoints = [
      '/api/products',
      '/api/catalog',
      '/api/search',
      '/api/categories/rotary-seals/products',
      'https://enext-cms.fst.com/products',
      'https://enext-cms.fst.com/catalog',
      'https://products.fst.com/api/products',
      'https://products.fst.com/api/catalog'
    ];
    
    for (const endpoint of testEndpoints) {
      try {
        console.log(`   🔍 Тестируем: ${endpoint}`);
        const response = await page.evaluate(async (url) => {
          try {
            const res = await fetch(url);
            return {
              status: res.status,
              contentType: res.headers.get('content-type'),
              bodyLength: (await res.text()).length
            };
          } catch (e) {
            return { error: e.message };
          }
        }, endpoint);
        
        if (response.error) {
          console.log(`      ❌ ${response.error}`);
        } else {
          console.log(`      ✅ ${response.status} ${response.contentType} (${response.bodyLength} chars)`);
        }
      } catch (e) {
        console.log(`      ❌ ${e}`);
      }
    }

  } catch (error) {
    console.log(`💥 Ошибка: ${error}`);
  } finally {
    if (browser) {
      await browser.close();
    }
  }

  console.log('\n🏁 Диагностика API продуктов завершена');
}

runProductApiDiagnostics().catch(console.error);
