import { FSTScraper } from './scraper';
import * as path from 'path';

async function testFixedScraper() {
  console.log('🧪 Тестирование исправленного скрапера');
  console.log('====================================\n');

  const outputDir = path.join(__dirname, '..', 'test-data');
  const logFile = path.join(__dirname, '..', 'test-scrape-log.json');
  
  const scraper = new FSTScraper({
    baseUrl: 'https://products.fst.com/global/en',
    outputDir,
    logFile,
    delay: 2000 // Увеличиваем задержку для стабильности
  });
  
  try {
    console.log('🚀 Запуск исправленного скрапера...');
    
    // Тестируем только получение категорий и первых продуктов
    console.log('1️⃣ Тестирование получения категорий...');
    
    // Создаем временный метод для тестирования
    const testGetCategories = async () => {
      // Инициализируем браузер
      await (scraper as any).initBrowser();
      
      try {
        // Вызываем приватный метод getCategories
        const categories = await (scraper as any).getCategories();
        return categories;
      } finally {
        // Закрываем браузер
        await (scraper as any).closeBrowser();
      }
    };
    
    const categories = await testGetCategories();
    
    console.log(`✅ Найдено ${categories.length} категорий:`);
    categories.forEach((cat: any, i: number) => {
      console.log(`   ${i + 1}. ${cat.name} (${cat.productUrls.length} продуктов)`);
      console.log(`      URL: ${cat.url}`);
      
      // Показываем первые 3 продукта
      if (cat.productUrls.length > 0) {
        console.log('      Примеры продуктов:');
        cat.productUrls.slice(0, 3).forEach((url: string, j: number) => {
          console.log(`         ${j + 1}. ${url}`);
        });
      }
      console.log('');
    });
    
    const totalProducts = categories.reduce((sum: number, cat: any) => sum + cat.productUrls.length, 0);
    console.log(`📊 Общее количество найденных продуктов: ${totalProducts}`);
    
    if (totalProducts > 0) {
      console.log('\n🎉 УСПЕХ! Исправленный скрапер работает!');
      console.log('Найдены продукты в подкатегориях.');
      
      // Тестируем скрапинг одного продукта
      console.log('\n2️⃣ Тестирование скрапинга одного продукта...');
      
      const categoryWithProducts = categories.find((cat: any) => cat.productUrls.length > 0);
      if (categoryWithProducts && categoryWithProducts.productUrls.length > 0) {
        const testProductUrl = categoryWithProducts.productUrls[0];
        console.log(`🔍 Тестируем продукт: ${testProductUrl}`);
        
        try {
          // Инициализируем браузер снова
          await (scraper as any).initBrowser();
          
          // Тестируем скрапинг продукта
          const productInfo = await (scraper as any).scrapeProduct(testProductUrl, categoryWithProducts.name);
          
          console.log('✅ Продукт успешно обработан:');
          console.log(`   ID: ${productInfo.id}`);
          console.log(`   Заголовок: ${productInfo.title}`);
          console.log(`   H1: ${productInfo.h1}`);
          console.log(`   Описание: ${productInfo.description.substring(0, 100)}...`);
          console.log(`   Характеристики: ${Object.keys(productInfo.specifications).length} шт.`);
          console.log(`   Изображения: ${productInfo.images.length} шт.`);
          
          await (scraper as any).closeBrowser();
          
        } catch (error) {
          console.log(`❌ Ошибка при тестировании продукта: ${error}`);
          await (scraper as any).closeBrowser();
        }
      }
      
    } else {
      console.log('\n❌ Продукты не найдены. Требуется дополнительная отладка.');
    }
    
  } catch (error) {
    console.error('💥 Ошибка тестирования:', error);
  }
  
  console.log('\n🏁 Тестирование завершено');
}

testFixedScraper().catch(console.error);
