import { chromium } from 'playwright';

async function testUrlPagination() {
  console.log('🔗 Тест пагинации через URL параметры');
  console.log('===================================\n');

  let browser;
  let page;

  try {
    browser = await chromium.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-dev-shm-usage']
    });

    page = await browser.newPage({
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    });
    
    await page.setViewportSize({ width: 1920, height: 1080 });
    page.setDefaultTimeout(60000);

    const baseUrl = 'https://products.fst.com/global/en/categories/rotary-seals/radial-shaft-seal-simmerring';
    
    console.log('1️⃣ Тестирование страницы 1...');
    console.log(`🌐 Переходим на: ${baseUrl}`);
    
    await page.goto(baseUrl, { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });
    
    await page.waitForTimeout(8000);

    // Прокрутка для загрузки контента
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight);
    });
    await page.waitForTimeout(3000);
    
    await page.evaluate(() => {
      window.scrollTo(0, 0);
    });
    await page.waitForTimeout(2000);

    const page1Products = await page.evaluate(() => {
      const productUrls: string[] = [];
      const elements = document.querySelectorAll('a[href*="/products/"]');
      elements.forEach(el => {
        const href = (el as HTMLAnchorElement).href;
        if (href) productUrls.push(href);
      });
      return Array.from(new Set(productUrls));
    });

    console.log(`📦 Страница 1: ${page1Products.length} продуктов`);
    console.log('📋 Примеры:');
    page1Products.slice(0, 3).forEach((url, i) => {
      console.log(`   ${i + 1}. ${url}`);
    });

    console.log('\n2️⃣ Анализ URL для пагинации...');
    
    // Проверяем различные URL параметры для пагинации
    const paginationUrls = [
      `${baseUrl}?page=2`,
      `${baseUrl}?p=2`,
      `${baseUrl}?pageIndex=2`,
      `${baseUrl}?pageNumber=2`,
      `${baseUrl}?offset=26`,
      `${baseUrl}?skip=26`,
      `${baseUrl}#page=2`
    ];

    let workingUrl = null;

    for (const testUrl of paginationUrls) {
      try {
        console.log(`🔍 Тестируем URL: ${testUrl}`);
        
        await page.goto(testUrl, { 
          waitUntil: 'domcontentloaded',
          timeout: 30000 
        });
        
        await page.waitForTimeout(5000);
        
        // Проверяем, изменилась ли страница
        const pageInfo = await page.evaluate(() => {
          const selectedButton = document.querySelector('.k-pager .k-selected');
          const ariaLabel = document.querySelector('.k-pager')?.getAttribute('aria-label') || '';
          return {
            selectedPage: selectedButton?.textContent?.trim() || '',
            ariaLabel,
            currentUrl: window.location.href
          };
        });
        
        console.log(`   📄 Выбранная страница: ${pageInfo.selectedPage}`);
        console.log(`   🔗 Текущий URL: ${pageInfo.currentUrl}`);
        
        if (pageInfo.selectedPage === '2') {
          console.log(`   ✅ НАЙДЕН РАБОЧИЙ URL: ${testUrl}`);
          workingUrl = testUrl;
          break;
        } else {
          console.log(`   ❌ Страница не изменилась`);
        }
        
      } catch (error) {
        console.log(`   ❌ Ошибка с URL ${testUrl}: ${error}`);
      }
    }

    if (workingUrl) {
      console.log(`\n🎉 Найден рабочий URL для пагинации: ${workingUrl}`);
      
      // Получаем продукты со страницы 2
      await page.evaluate(() => {
        window.scrollTo(0, document.body.scrollHeight);
      });
      await page.waitForTimeout(3000);
      
      await page.evaluate(() => {
        window.scrollTo(0, 0);
      });
      await page.waitForTimeout(2000);
      
      const page2Products = await page.evaluate(() => {
        const productUrls: string[] = [];
        const elements = document.querySelectorAll('a[href*="/products/"]');
        elements.forEach(el => {
          const href = (el as HTMLAnchorElement).href;
          if (href) productUrls.push(href);
        });
        return Array.from(new Set(productUrls));
      });
      
      console.log(`📦 Страница 2: ${page2Products.length} продуктов`);
      
      const differentProducts = page2Products.filter(url => !page1Products.includes(url));
      console.log(`🔄 Новых продуктов: ${differentProducts.length}`);
      
      if (differentProducts.length > 0) {
        console.log('✅ Продукты на странице 2 отличаются!');
        console.log('📋 Примеры новых продуктов:');
        differentProducts.slice(0, 3).forEach((url, i) => {
          console.log(`   ${i + 1}. ${url}`);
        });
        
        console.log('\n3️⃣ Тест страницы 3...');
        
        const page3Url = workingUrl.replace('=2', '=3');
        console.log(`🔍 Тестируем страницу 3: ${page3Url}`);
        
        await page.goto(page3Url, { 
          waitUntil: 'domcontentloaded',
          timeout: 30000 
        });
        
        await page.waitForTimeout(5000);
        
        const page3Info = await page.evaluate(() => {
          const selectedButton = document.querySelector('.k-pager .k-selected');
          return selectedButton?.textContent?.trim() || '';
        });
        
        console.log(`📄 Страница 3: ${page3Info}`);
        
        if (page3Info === '3') {
          console.log('✅ Переход на страницу 3 успешен!');
          
          console.log('\n🎯 ИТОГОВЫЕ РЕЗУЛЬТАТЫ:');
          console.log(`✅ Рабочий URL паттерн: ${workingUrl}`);
          console.log('✅ Пагинация работает через URL параметры');
          console.log('✅ Продукты на разных страницах отличаются');
          console.log('✅ Можно переходить между страницами');
          
        } else {
          console.log('⚠️ Переход на страницу 3 не удался');
        }
        
      } else {
        console.log('⚠️ Продукты на странице 2 такие же, как на странице 1');
      }
      
    } else {
      console.log('\n❌ Рабочий URL для пагинации не найден');
      
      console.log('\n4️⃣ Альтернативный подход - анализ ссылок пагинации...');
      
      // Возвращаемся на первую страницу
      await page.goto(baseUrl, { 
        waitUntil: 'domcontentloaded',
        timeout: 30000 
      });
      
      await page.waitForTimeout(5000);
      
      // Ищем ссылки в пагинации
      const paginationLinks = await page.evaluate(() => {
        const links: Array<{text: string, href: string}> = [];
        
        // Ищем все ссылки в пагинации
        const pagerElements = document.querySelectorAll('.k-pager a, .k-pager button[onclick], .k-pager [data-page]');
        pagerElements.forEach(el => {
          const text = el.textContent?.trim() || '';
          const href = (el as HTMLAnchorElement).href || '';
          const onclick = el.getAttribute('onclick') || '';
          const dataPage = el.getAttribute('data-page') || '';
          
          if (href || onclick || dataPage) {
            links.push({
              text,
              href: href || `onclick: ${onclick}` || `data-page: ${dataPage}`
            });
          }
        });
        
        return links;
      });
      
      console.log('📊 Найденные ссылки пагинации:');
      paginationLinks.forEach((link, i) => {
        console.log(`   ${i + 1}. "${link.text}" → ${link.href}`);
      });
      
      if (paginationLinks.length === 0) {
        console.log('❌ Ссылки пагинации не найдены');
        console.log('💡 Возможно, пагинация работает через JavaScript события');
      }
    }

  } catch (error) {
    console.log(`💥 Ошибка: ${error}`);
  } finally {
    if (browser) {
      await browser.close();
    }
  }

  console.log('\n🏁 Тест завершен');
}

testUrlPagination().catch(console.error);
