import { chromium } from 'playwright';

async function testPageNumbers() {
  console.log('🔢 Тест пагинации через номера страниц');
  console.log('===================================\n');

  let browser;
  let page;

  try {
    browser = await chromium.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-dev-shm-usage']
    });

    page = await browser.newPage({
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    });
    
    await page.setViewportSize({ width: 1920, height: 1080 });
    page.setDefaultTimeout(60000);

    const testUrl = 'https://products.fst.com/global/en/categories/rotary-seals/radial-shaft-seal-simmerring';
    
    console.log(`🌐 Переходим на: ${testUrl}`);
    await page.goto(testUrl, { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });
    
    await page.waitForTimeout(8000);

    console.log('1️⃣ Анализ кнопок номеров страниц...');
    
    const pageButtonsInfo = await page.evaluate(() => {
      const buttons = document.querySelectorAll('.k-pager button');
      const pageButtons: Array<{
        text: string,
        classes: string,
        isSelected: boolean,
        isDisabled: boolean
      }> = [];
      
      buttons.forEach(btn => {
        const text = btn.textContent?.trim() || '';
        const classes = btn.className || '';
        const isSelected = classes.includes('k-selected');
        const isDisabled = classes.includes('k-disabled');
        
        // Ищем кнопки с номерами страниц
        if (/^\d+$/.test(text)) {
          pageButtons.push({
            text,
            classes,
            isSelected,
            isDisabled
          });
        }
      });
      
      return pageButtons;
    });

    console.log('📊 Найденные кнопки страниц:');
    pageButtonsInfo.forEach((btn, i) => {
      const status = btn.isSelected ? '🔵' : btn.isDisabled ? '❌' : '⚪';
      console.log(`   ${status} Страница ${btn.text} (${btn.isSelected ? 'выбрана' : 'доступна'})`);
    });

    console.log('\n2️⃣ Получение продуктов со страницы 1...');
    
    // Прокрутка для загрузки контента
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight);
    });
    await page.waitForTimeout(3000);
    
    await page.evaluate(() => {
      window.scrollTo(0, 0);
    });
    await page.waitForTimeout(2000);

    const page1Products = await page.evaluate(() => {
      const productUrls: string[] = [];
      const elements = document.querySelectorAll('a[href*="/products/"]');
      elements.forEach(el => {
        const href = (el as HTMLAnchorElement).href;
        if (href) productUrls.push(href);
      });
      return Array.from(new Set(productUrls));
    });

    console.log(`📦 Страница 1: ${page1Products.length} продуктов`);
    console.log('📋 Примеры:');
    page1Products.slice(0, 3).forEach((url, i) => {
      console.log(`   ${i + 1}. ${url}`);
    });

    console.log('\n3️⃣ Переход на страницу 2 через кнопку номера...');
    
    try {
      // Ищем кнопку "2"
      const page2Button = await page.$('.k-pager button:has-text("2"):not(.k-disabled)');
      
      if (page2Button) {
        console.log('✅ Кнопка "2" найдена');
        
        // Пробуем разные способы клика
        try {
          console.log('🔄 Кликаем на кнопку "2"...');
          await page2Button.click({ force: true });
          await page.waitForTimeout(5000);
        } catch (e) {
          console.log('⚠️ Обычный клик не сработал, пробуем JavaScript...');
          await page2Button.evaluate(el => (el as HTMLElement).click());
          await page.waitForTimeout(5000);
        }
        
        // Проверяем результат
        const currentPageInfo = await page.evaluate(() => {
          const selectedButton = document.querySelector('.k-pager .k-selected');
          const ariaLabel = document.querySelector('.k-pager')?.getAttribute('aria-label') || '';
          return {
            selectedPage: selectedButton?.textContent?.trim() || '',
            ariaLabel
          };
        });
        
        console.log(`📄 Текущая страница: ${currentPageInfo.selectedPage}`);
        console.log(`📊 Aria-label: ${currentPageInfo.ariaLabel}`);
        
        if (currentPageInfo.selectedPage === '2') {
          console.log('✅ Успешно перешли на страницу 2!');
          
          // Получаем продукты со страницы 2
          await page.evaluate(() => {
            window.scrollTo(0, document.body.scrollHeight);
          });
          await page.waitForTimeout(3000);
          
          await page.evaluate(() => {
            window.scrollTo(0, 0);
          });
          await page.waitForTimeout(2000);
          
          const page2Products = await page.evaluate(() => {
            const productUrls: string[] = [];
            const elements = document.querySelectorAll('a[href*="/products/"]');
            elements.forEach(el => {
              const href = (el as HTMLAnchorElement).href;
              if (href) productUrls.push(href);
            });
            return Array.from(new Set(productUrls));
          });
          
          console.log(`📦 Страница 2: ${page2Products.length} продуктов`);
          
          const differentProducts = page2Products.filter(url => !page1Products.includes(url));
          console.log(`🔄 Новых продуктов: ${differentProducts.length}`);
          
          if (differentProducts.length > 0) {
            console.log('✅ Продукты на странице 2 отличаются!');
            console.log('📋 Примеры новых продуктов:');
            differentProducts.slice(0, 3).forEach((url, i) => {
              console.log(`   ${i + 1}. ${url}`);
            });
            
            console.log('\n4️⃣ Тест перехода на страницу 3...');
            
            const page3Button = await page.$('.k-pager button:has-text("3"):not(.k-disabled)');
            if (page3Button) {
              try {
                await page3Button.click({ force: true });
              } catch (e) {
                await page3Button.evaluate(el => (el as HTMLElement).click());
              }
              
              await page.waitForTimeout(5000);
              
              const page3Info = await page.evaluate(() => {
                const selectedButton = document.querySelector('.k-pager .k-selected');
                return selectedButton?.textContent?.trim() || '';
              });
              
              console.log(`📄 Страница 3: ${page3Info}`);
              
              if (page3Info === '3') {
                console.log('✅ Переход на страницу 3 успешен!');
                
                console.log('\n🎯 ИТОГОВЫЕ РЕЗУЛЬТАТЫ:');
                console.log('✅ Пагинация работает через кнопки номеров страниц');
                console.log('✅ Селектор: .k-pager button:has-text("N"):not(.k-disabled)');
                console.log('✅ Продукты на разных страницах отличаются');
                console.log('✅ Можно переходить между страницами');
                console.log(`✅ Всего страниц: ${currentPageInfo.ariaLabel.match(/of (\d+)/)?.[1] || 'неизвестно'}`);
                
              } else {
                console.log('⚠️ Переход на страницу 3 не удался');
              }
            } else {
              console.log('❌ Кнопка "3" не найдена');
            }
            
          } else {
            console.log('⚠️ Продукты на странице 2 такие же, как на странице 1');
          }
          
        } else {
          console.log('❌ Не удалось перейти на страницу 2');
        }
        
      } else {
        console.log('❌ Кнопка "2" не найдена');
      }
      
    } catch (error) {
      console.log(`❌ Ошибка при переходе на страницу 2: ${error}`);
    }

  } catch (error) {
    console.log(`💥 Ошибка: ${error}`);
  } finally {
    if (browser) {
      await browser.close();
    }
  }

  console.log('\n🏁 Тест завершен');
}

testPageNumbers().catch(console.error);
