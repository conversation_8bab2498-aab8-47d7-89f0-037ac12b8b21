import { chrom<PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';
import * as fs from 'fs-extra';
import * as path from 'path';
import { ScraperConfig, ProductInfo, CategoryInfo, SubcategoryInfo } from './types';
import { ScrapingLogger } from './logger';

export class FSTScraperNew {
  private config: ScraperConfig;
  private logger: ScrapingLogger;
  private browser?: Browser;
  private page?: Page;

  constructor(config: ScraperConfig) {
    this.config = {
      delay: 2000,
      maxRetries: 3,
      ...config
    };
    this.logger = new ScrapingLogger(config.logFile);
    
    // Ensure output directory exists
    fs.ensureDirSync(config.outputDir);
  }

  async start(): Promise<void> {
    try {
      this.logger.logInfo('Starting FST product scraper...');
      
      await this.initBrowser();
      
      // Get all main categories
      const categories = await this.getMainCategories();
      this.logger.logInfo(`Found ${categories.length} main categories`);
      
      // Process each category completely before moving to next
      for (const category of categories) {
        await this.processCategory(category);
        await this.delay();
      }
      
      this.logger.logInfo('Scraping completed successfully!');
      
    } catch (error) {
      this.logger.logError('Scraping failed', error);
      throw error;
    } finally {
      await this.closeBrowser();
    }
  }

  private async initBrowser(): Promise<void> {
    this.logger.logInfo('Launching browser...');
    
    const maxRetries = 3;
    let attempt = 0;
    
    while (attempt < maxRetries) {
      try {
        this.browser = await chromium.launch({ 
          headless: true,
          args: ['--no-sandbox', '--disable-dev-shm-usage']
        });
        
        this.page = await this.browser.newPage({
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        });
        
        await this.page.setViewportSize({ width: 1920, height: 1080 });
        this.page.setDefaultTimeout(60000);
        this.page.setDefaultNavigationTimeout(60000);
        
        this.logger.logInfo('Browser launched successfully');
        return;
        
      } catch (error) {
        attempt++;
        this.logger.logError(`Failed to launch browser, attempt ${attempt}/${maxRetries}:`, error);
        
        if (this.browser) {
          try {
            await this.browser.close();
          } catch (e) {
            // Ignore errors
          }
          this.browser = undefined;
          this.page = undefined;
        }
        
        if (attempt >= maxRetries) {
          throw new Error(`Failed to launch browser after ${maxRetries} attempts. Last error: ${error}`);
        }
        
        await new Promise(resolve => setTimeout(resolve, 2000 * attempt));
      }
    }
  }

  private async closeBrowser(): Promise<void> {
    if (this.browser) {
      try {
        await this.browser.close();
      } catch (error) {
        this.logger.logError('Error closing browser:', error);
      } finally {
        this.browser = undefined;
        this.page = undefined;
      }
    }
  }

  private async getMainCategories(): Promise<CategoryInfo[]> {
    if (!this.page) throw new Error('Browser not initialized');

    this.logger.logInfo('Fetching main categories...');
    await this.page.goto(this.config.baseUrl, { 
      waitUntil: 'domcontentloaded',
      timeout: 60000 
    });
    
    await this.page.waitForTimeout(5000);

    // Get main category links
    const mainCategoryLinks = await this.page.$$eval('a[href*="/categories/"]', links => {
      return links.map(link => ({
        name: link.textContent?.trim() || '',
        url: (link as HTMLAnchorElement).href
      })).filter(cat => {
        const hasName = cat.name && cat.url;
        const isMainCategory = cat.url.includes('/categories/') && 
                              !cat.url.endsWith('/categories') &&
                              cat.name.length > 2 &&
                              (cat.url.match(/\/categories\/[^\/]+$/));
        return hasName && isMainCategory;
      });
    });

    const categories: CategoryInfo[] = [];
    
    for (const mainCategoryLink of mainCategoryLinks) {
      try {
        this.logger.logInfo(`Processing main category: ${mainCategoryLink.name}`);
        
        await this.page.goto(mainCategoryLink.url, { 
          waitUntil: 'domcontentloaded',
          timeout: 60000 
        });
        await this.page.waitForTimeout(5000);
        
        // Get subcategories
        const subcategoryLinks = await this.page.$$eval('.category-tile__link, a.cardTile', links => {
          return links.map(link => ({
            name: link.textContent?.trim() || '',
            url: (link as HTMLAnchorElement).href
          })).filter(subcat => {
            const hasName = subcat.name && subcat.url;
            const isSubcategory = subcat.url.includes('/categories/') && 
                                 subcat.name.length > 2;
            return hasName && isSubcategory;
          });
        });
        
        this.logger.logInfo(`Found ${subcategoryLinks.length} subcategories in ${mainCategoryLink.name}`);
        
        categories.push({
          name: mainCategoryLink.name,
          url: mainCategoryLink.url,
          subcategories: subcategoryLinks.map(sub => ({
            name: sub.name,
            url: sub.url,
            images: [],
            products: [],
            totalPages: 0
          })),
          totalProducts: 0,
          scraped: false
        });
        
      } catch (error) {
        this.logger.logError(`Failed to process main category ${mainCategoryLink.name}`, error);
      }
    }

    return categories;
  }

  private async processCategory(category: CategoryInfo): Promise<void> {
    this.logger.logInfo(`Processing category: ${category.name}`);
    
    // Process each subcategory
    for (const subcategory of category.subcategories) {
      await this.processSubcategory(category, subcategory);
      await this.delay();
    }
    
    // Save category data to file
    await this.saveCategoryData(category);
    category.scraped = true;
    
    this.logger.logInfo(`Completed category: ${category.name} (${category.totalProducts} products)`);
  }

  async processSubcategory(category: CategoryInfo, subcategory: SubcategoryInfo): Promise<void> {
    if (!this.page) throw new Error('Browser not initialized');

    this.logger.logInfo(`Processing subcategory: ${subcategory.name}`);

    try {
      await this.page.goto(subcategory.url, {
        waitUntil: 'domcontentloaded',
        timeout: 60000
      });

      await this.page.waitForTimeout(8000);

      // Get subcategory info and images
      const subcategoryInfo = await this.page.evaluate(() => {
        const images: string[] = [];
        const imgElements = document.querySelectorAll('img');
        imgElements.forEach(img => {
          if (img.src && !img.src.includes('data:')) {
            images.push(img.src);
          }
        });

        return {
          description: document.querySelector('meta[name="description"]')?.getAttribute('content') || '',
          images: images.slice(0, 10) // Limit to 10 images
        };
      });

      subcategory.description = subcategoryInfo.description;
      subcategory.images = subcategoryInfo.images;

      // Get all products from all pages
      await this.scrapeAllProductsFromSubcategory(subcategory);

      subcategory.scrapedAt = new Date().toISOString();
      category.totalProducts += subcategory.products.length;

      this.logger.logInfo(`Completed subcategory: ${subcategory.name} (${subcategory.products.length} products, ${subcategory.totalPages} pages)`);

    } catch (error) {
      this.logger.logError(`Failed to process subcategory ${subcategory.name}`, error);
    }
  }

  private async scrapeAllProductsFromSubcategory(subcategory: SubcategoryInfo): Promise<void> {
    if (!this.page) throw new Error('Browser not initialized');

    let currentPage = 1;
    let hasNextPage = true;
    const productsPerPage = 26; // Найдено в тестах

    while (hasNextPage && currentPage <= 10) { // Ограничиваем до 10 страниц для тестирования
      this.logger.logInfo(`Scraping page ${currentPage} of ${subcategory.name}`);

      // Формируем URL с пагинацией
      const skip = (currentPage - 1) * productsPerPage;
      const pageUrl = skip > 0 ? `${subcategory.url}?skip=${skip}` : subcategory.url;

      try {
        await this.page.goto(pageUrl, {
          waitUntil: 'domcontentloaded',
          timeout: 30000
        });

        // Wait for products to load
        await this.page.waitForTimeout(8000);

        // Scroll to trigger lazy loading
        await this.page.evaluate(() => {
          window.scrollTo(0, document.body.scrollHeight);
        });
        await this.page.waitForTimeout(3000);

        await this.page.evaluate(() => {
          window.scrollTo(0, 0);
        });
        await this.page.waitForTimeout(2000);

        // Get product URLs from current page
        const productUrls = await this.page.$$eval('a[href*="product"]', links => {
          return Array.from(new Set(
            links.map(link => (link as HTMLAnchorElement).href)
              .filter(url => url.includes('/products/'))
          ));
        });

        this.logger.logInfo(`Found ${productUrls.length} products on page ${currentPage}`);

        // Если продуктов нет, значит достигли конца
        if (productUrls.length === 0) {
          hasNextPage = false;
          break;
        }

        // Scrape each product on this page
        for (const productUrl of productUrls) {
          try {
            const product = await this.scrapeProduct(productUrl, subcategory.name);
            if (product) {
              product.category = subcategory.name; // Устанавливаем категорию
              subcategory.products.push(product);
            }
            await this.delay(1000); // Short delay between products
          } catch (error) {
            this.logger.logError(`Failed to scrape product ${productUrl}`, error);
          }
        }

        // Проверяем, есть ли следующая страница
        const paginationInfo = await this.page.evaluate(() => {
          const pager = document.querySelector('.k-pager');
          if (!pager) return { hasNext: false, totalPages: 1 };

          const ariaLabel = pager.getAttribute('aria-label') || '';
          const match = ariaLabel.match(/page (\d+) of (\d+)/);

          if (match) {
            const currentPageNum = parseInt(match[1]);
            const totalPages = parseInt(match[2]);
            return {
              hasNext: currentPageNum < totalPages,
              totalPages,
              currentPageNum
            };
          }

          return { hasNext: false, totalPages: 1 };
        });

        if (paginationInfo.hasNext && currentPage < paginationInfo.totalPages) {
          currentPage++;
        } else {
          hasNextPage = false;
        }

        subcategory.totalPages = paginationInfo.totalPages || currentPage;

      } catch (error) {
        this.logger.logError(`Error scraping page ${currentPage}: ${error}`);
        hasNextPage = false;
      }
    }

    this.logger.logInfo(`Completed ${subcategory.name}: ${subcategory.products.length} products from ${currentPage} pages`);
  }

  private async scrapeProduct(productUrl: string, subcategoryName: string): Promise<ProductInfo | null> {
    if (!this.page) throw new Error('Browser not initialized');

    try {
      await this.page.goto(productUrl, {
        waitUntil: 'domcontentloaded',
        timeout: 30000
      });

      await this.page.waitForTimeout(3000);

      const productData = await this.page.evaluate(() => {
        const getTextContent = (selector: string): string => {
          const element = document.querySelector(selector);
          return element?.textContent?.trim() || '';
        };

        // Extract FST item number from URL or page
        const fstItemNo = window.location.pathname.split('/').pop() || '';

        // Extract basic info
        const title = getTextContent('h1') || document.title;
        const description = document.querySelector('meta[name="description"]')?.getAttribute('content') || '';

        // Extract specifications from product details table
        const specifications: Record<string, string> = {};
        const dimensions: Record<string, string> = {};

        // Parse product details table (.product-details-main__table__row)
        const detailRows = document.querySelectorAll('.product-details-main__table__row');
        detailRows.forEach(row => {
          const label = row.querySelector('.product-details-main__table__row__label')?.textContent?.trim() || '';
          const value = row.querySelector('.product-details-main__table__row__value')?.textContent?.trim() || '';

          if (label && value && value !== '-') {
            specifications[label] = value;
          }
        });

        // Parse dimensions from dimensions container
        const dimensionElements = document.querySelectorAll('.dimensions-container__dimensions-details > div');
        dimensionElements.forEach(dimEl => {
          const text = dimEl.textContent?.trim() || '';
          const lines = text.split('\n').map(line => line.trim()).filter(line => line);

          if (lines.length >= 2) {
            const key = lines[0];
            const value = lines[1];
            if (key && value) {
              dimensions[key] = value;
            }
          }
        });

        // Also try to parse dimensions from text patterns
        const dimensionPatterns = [
          /Inner diameter \(d1\)\s*([^\n]+)/,
          /Outer diameter \(d2\)\s*([^\n]+)/,
          /Seal width \(b\)\s*([^\n]+)/,
          /Spring\s*([^\n]+)/,
          /Material spring\s*([^\n]+)/,
          /Material of metal insert \(WKSTT\)\s*([^\n]+)/
        ];

        const pageText = document.body.textContent || '';
        dimensionPatterns.forEach(pattern => {
          const match = pageText.match(pattern);
          if (match) {
            const key = match[0].split(/\s*\d/)[0].trim();
            const value = match[1].trim();
            if (key && value) {
              dimensions[key] = value;
            }
          }
        });

        // Extract images
        const images: string[] = [];
        const imgElements = document.querySelectorAll('img');
        imgElements.forEach(img => {
          if (img.src && !img.src.includes('data:') && !img.src.includes('icon') && !img.src.includes('logo')) {
            images.push(img.src);
          }
        });

        return {
          fstItemNo,
          title,
          description,
          specifications,
          dimensions,
          images: Array.from(new Set(images)), // Remove duplicates
          url: window.location.href
        };
      });

      // Extract specific product fields if available
      const additionalData = await this.extractProductFields();

      const product: ProductInfo = {
        fstItemNo: productData.fstItemNo,
        title: productData.title,
        description: productData.description,
        specifications: productData.specifications,
        dimensions: productData.dimensions,
        images: productData.images,
        category: '', // Will be set by caller
        subcategory: subcategoryName,
        url: productData.url,
        scrapedAt: new Date().toISOString(),
        ...additionalData
      };

      return product;

    } catch (error) {
      this.logger.logError(`Failed to scrape product ${productUrl}`, error);
      return null;
    }
  }

  private async extractProductFields(): Promise<Partial<ProductInfo>> {
    if (!this.page) return {};

    return await this.page.evaluate(() => {
      const getFieldFromTable = (label: string): string => {
        const rows = document.querySelectorAll('.product-details-main__table__row');
        for (const row of rows) {
          const labelEl = row.querySelector('.product-details-main__table__row__label');
          const valueEl = row.querySelector('.product-details-main__table__row__value');

          if (labelEl && valueEl) {
            const labelText = labelEl.textContent?.trim().toLowerCase() || '';
            const valueText = valueEl.textContent?.trim() || '';

            if (labelText.includes(label.toLowerCase()) && valueText && valueText !== '-') {
              return valueText;
            }
          }
        }
        return '';
      };

      return {
        legacyItemNo: getFieldFromTable('legacy item no'),
        successorItem: getFieldFromTable('successor item'),
        variant: getFieldFromTable('variant'),
        netWeight: getFieldFromTable('net weight'),
        packaging: getFieldFromTable('packaging'),
        reach: getFieldFromTable('reach'),
        rohs: getFieldFromTable('rohs'),
        brand: getFieldFromTable('brand'),
        material: getFieldFromTable('material')
      };
    });
  }

  private async saveCategoryData(category: CategoryInfo): Promise<void> {
    const fileName = `${category.name.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase()}.json`;
    const filePath = path.join(this.config.outputDir, fileName);

    const categoryData = {
      category: category.name,
      url: category.url,
      totalProducts: category.totalProducts,
      scrapedAt: new Date().toISOString(),
      subcategories: category.subcategories.map(sub => ({
        name: sub.name,
        url: sub.url,
        description: sub.description,
        images: sub.images,
        totalPages: sub.totalPages,
        totalProducts: sub.products.length,
        products: sub.products
      }))
    };

    await fs.writeJSON(filePath, categoryData, { spaces: 2 });
    this.logger.logInfo(`Saved category data to ${fileName}`);
  }

  private async delay(ms?: number): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, ms || this.config.delay || 2000));
  }
}
