import { chromium } from 'playwright';

async function testCorrectPagination() {
  console.log('🎯 Тест правильной пагинации с кнопками');
  console.log('====================================\n');

  let browser;
  let page;

  try {
    browser = await chromium.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-dev-shm-usage']
    });

    page = await browser.newPage({
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    });
    
    await page.setViewportSize({ width: 1920, height: 1080 });
    page.setDefaultTimeout(60000);

    const testUrl = 'https://products.fst.com/global/en/categories/rotary-seals/radial-shaft-seal-simmerring';
    
    console.log(`🌐 Переходим на: ${testUrl}`);
    await page.goto(testUrl, { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });
    
    await page.waitForTimeout(8000);

    console.log('1️⃣ Проверка продуктов на странице 1...');
    
    // Прокрутка для загрузки контента
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight);
    });
    await page.waitForTimeout(3000);
    
    await page.evaluate(() => {
      window.scrollTo(0, 0);
    });
    await page.waitForTimeout(2000);

    const page1Products = await page.evaluate(() => {
      const productUrls: string[] = [];
      const elements = document.querySelectorAll('a[href*="/products/"]');
      elements.forEach(el => {
        const href = (el as HTMLAnchorElement).href;
        if (href) productUrls.push(href);
      });
      return Array.from(new Set(productUrls));
    });

    console.log(`📦 Страница 1: найдено ${page1Products.length} уникальных продуктов`);
    console.log('📋 Примеры продуктов:');
    page1Products.slice(0, 3).forEach((url, i) => {
      console.log(`   ${i + 1}. ${url}`);
    });

    console.log('\n2️⃣ Поиск правильной кнопки Next...');
    
    const nextButtonSelectors = [
      '.k-pager button[title*="next"]',
      '.k-pager .k-pager-nav:not(.k-disabled):not(.k-pager-first)',
      '.k-pager .k-pager-nav:last-of-type',
      '.k-pager button:has(.k-svg-i-caret-alt-right)',
      '.k-pager button .k-svg-i-caret-alt-right',
      '.k-pager .k-svg-i-caret-alt-right'
    ];

    let workingSelector = null;

    for (const selector of nextButtonSelectors) {
      try {
        console.log(`🔍 Тестируем селектор: ${selector}`);
        
        const elements = await page.$$(selector);
        console.log(`   Найдено элементов: ${elements.length}`);
        
        if (elements.length > 0) {
          for (let i = 0; i < elements.length; i++) {
            const element = elements[i];
            const elementInfo = await element.evaluate(el => ({
              text: el.textContent?.trim() || '',
              classes: el.className || '',
              title: el.getAttribute('title') || '',
              disabled: el.classList.contains('k-disabled') || 
                       el.classList.contains('disabled') ||
                       el.getAttribute('aria-disabled') === 'true',
              tagName: el.tagName.toLowerCase()
            }));
            
            console.log(`   Элемент ${i + 1}:`);
            console.log(`     Тег: ${elementInfo.tagName}`);
            console.log(`     Текст: "${elementInfo.text}"`);
            console.log(`     Title: "${elementInfo.title}"`);
            console.log(`     Классы: ${elementInfo.classes}`);
            console.log(`     Отключен: ${elementInfo.disabled}`);
            
            // Если это кнопка Next и она не отключена
            if (!elementInfo.disabled && 
                (elementInfo.title.toLowerCase().includes('next') || 
                 elementInfo.classes.includes('caret-alt-right'))) {
              
              console.log(`   🎯 Найдена рабочая кнопка Next!`);
              workingSelector = selector;
              
              // Пробуем кликнуть
              console.log(`   🔄 Кликаем на кнопку...`);
              
              try {
                await element.click();
                await page.waitForTimeout(5000); // Ждем загрузки
                
                // Проверяем, что страница изменилась
                const currentUrl = page.url();
                console.log(`   📍 Текущий URL: ${currentUrl}`);
                
                // Проверяем продукты на новой странице
                const page2Products = await page.evaluate(() => {
                  const productUrls: string[] = [];
                  const elements = document.querySelectorAll('a[href*="/products/"]');
                  elements.forEach(el => {
                    const href = (el as HTMLAnchorElement).href;
                    if (href) productUrls.push(href);
                  });
                  return Array.from(new Set(productUrls));
                });
                
                console.log(`   📦 Страница 2: найдено ${page2Products.length} продуктов`);
                
                // Проверяем, что продукты отличаются
                const differentProducts = page2Products.filter(url => !page1Products.includes(url));
                console.log(`   🔄 Новых продуктов: ${differentProducts.length}`);
                
                if (differentProducts.length > 0) {
                  console.log(`   ✅ УСПЕХ! Пагинация работает!`);
                  console.log('   📋 Примеры новых продуктов:');
                  differentProducts.slice(0, 3).forEach((url, j) => {
                    console.log(`      ${j + 1}. ${url}`);
                  });
                  
                  // Проверим номер текущей страницы
                  const currentPageInfo = await page.evaluate(() => {
                    const selectedButton = document.querySelector('.k-pager .k-selected');
                    const ariaLabel = document.querySelector('.k-pager')?.getAttribute('aria-label') || '';
                    return {
                      selectedPage: selectedButton?.textContent?.trim() || '',
                      ariaLabel
                    };
                  });
                  
                  console.log(`   📄 Текущая страница: ${currentPageInfo.selectedPage}`);
                  console.log(`   📊 Aria-label: ${currentPageInfo.ariaLabel}`);
                  
                  break;
                } else {
                  console.log(`   ⚠️ Продукты не изменились`);
                }
                
              } catch (clickError) {
                console.log(`   ❌ Ошибка клика: ${clickError}`);
              }
            }
          }
          
          if (workingSelector) break;
        }
        
      } catch (error) {
        console.log(`   ❌ Ошибка с селектором ${selector}: ${error}`);
      }
    }

    if (workingSelector) {
      console.log(`\n🎉 НАЙДЕН РАБОЧИЙ СЕЛЕКТОР: ${workingSelector}`);
      
      console.log('\n3️⃣ Тест перехода на страницу 3...');
      
      try {
        // Ищем кнопку Next снова
        const nextButton = await page.$(workingSelector);
        if (nextButton) {
          const isDisabled = await nextButton.evaluate(el => 
            el.classList.contains('k-disabled') || el.classList.contains('disabled')
          );
          
          if (!isDisabled) {
            await nextButton.click();
            await page.waitForTimeout(5000);
            
            const page3Products = await page.evaluate(() => {
              const productUrls: string[] = [];
              const elements = document.querySelectorAll('a[href*="/products/"]');
              elements.forEach(el => {
                const href = (el as HTMLAnchorElement).href;
                if (href) productUrls.push(href);
              });
              return Array.from(new Set(productUrls));
            });
            
            console.log(`📦 Страница 3: найдено ${page3Products.length} продуктов`);
            
            const currentPageInfo = await page.evaluate(() => {
              const selectedButton = document.querySelector('.k-pager .k-selected');
              return selectedButton?.textContent?.trim() || '';
            });
            
            console.log(`📄 Текущая страница: ${currentPageInfo}`);
            
            if (currentPageInfo === '3') {
              console.log('✅ Переход на страницу 3 успешен!');
            }
          }
        }
      } catch (error) {
        console.log(`❌ Ошибка перехода на страницу 3: ${error}`);
      }
      
    } else {
      console.log('\n❌ Рабочий селектор для кнопки Next не найден');
    }

  } catch (error) {
    console.log(`💥 Ошибка: ${error}`);
  } finally {
    if (browser) {
      await browser.close();
    }
  }

  console.log('\n🏁 Тест завершен');
}

testCorrectPagination().catch(console.error);
