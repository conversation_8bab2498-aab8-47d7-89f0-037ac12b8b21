import { chromium } from 'playwright';

async function testAlternativeSelectors() {
  console.log('🔍 Тест альтернативных селекторов продуктов');
  console.log('==========================================\n');

  let browser;
  let page;

  try {
    browser = await chromium.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-dev-shm-usage']
    });

    page = await browser.newPage();
    
    const url = 'https://products.fst.com/global/en/categories/rotary-seals';
    await page.goto(url, { waitUntil: 'domcontentloaded', timeout: 30000 });
    
    // Ждем дольше для загрузки динамического контента
    console.log('⏳ Ожидание 10 секунд для загрузки динамического контента...');
    await page.waitForTimeout(10000);

    console.log('1️⃣ Тестирование различных селекторов...');
    
    const selectorTests = await page.evaluate(() => {
      const selectors = [
        // Стандартные селекторы продуктов
        'a[href*="/products/"]',
        'a[href*="/product/"]',
        'a[href*="product"]',
        
        // Селекторы по классам
        '.product-link',
        '.product-item a',
        '.product-card a',
        '.product a',
        '.item a',
        '.card a',
        
        // Селекторы по data-атрибутам
        '[data-product]',
        '[data-product-id]',
        '[data-product-url]',
        '[data-href*="product"]',
        
        // Селекторы по тексту или содержимому
        'a[title*="product"]',
        'a[title*="Product"]',
        
        // Общие селекторы ссылок
        'a[href*="/en/"]',
        'a[href*="/global/"]',
        'a[href^="/"]',
        
        // Селекторы для карточек или элементов списка
        '.grid a',
        '.list a',
        '.catalog a',
        '.results a',
        
        // Vue/React компоненты
        '[data-v-] a',
        '[class*="product"]',
        '[class*="item"]',
        '[class*="card"]'
      ];
      
      const results: Record<string, number> = {};
      
      selectors.forEach(selector => {
        try {
          const elements = document.querySelectorAll(selector);
          results[selector] = elements.length;
        } catch (e) {
          results[selector] = -1;
        }
      });
      
      return results;
    });
    
    console.log('📊 Результаты тестирования селекторов:');
    Object.entries(selectorTests).forEach(([selector, count]) => {
      const status = count > 0 ? '✅' : count === 0 ? '⚠️' : '❌';
      if (count > 0) {
        console.log(`   ${status} ${selector}: ${count} элементов`);
      }
    });
    
    // Найдем селекторы с результатами
    const workingSelectors = Object.entries(selectorTests)
      .filter(([_, count]) => count > 0)
      .sort(([_, a], [__, b]) => b - a);
    
    if (workingSelectors.length > 0) {
      console.log('\n2️⃣ Анализ найденных ссылок...');
      
      for (const [selector, count] of workingSelectors.slice(0, 5)) {
        console.log(`\n🔍 Анализ селектора: ${selector} (${count} элементов)`);
        
        const linkAnalysis = await page.evaluate((sel) => {
          const elements = document.querySelectorAll(sel);
          const links: Array<{href: string, text: string, title: string}> = [];
          
          elements.forEach((el, index) => {
            if (index < 10) { // Ограничиваем до 10 элементов
              const link = el as HTMLAnchorElement;
              links.push({
                href: link.href || '',
                text: (link.textContent || '').trim().substring(0, 50),
                title: link.title || link.getAttribute('title') || ''
              });
            }
          });
          
          return links;
        }, selector);
        
        linkAnalysis.forEach((link, i) => {
          console.log(`   ${i + 1}. ${link.href}`);
          if (link.text) console.log(`      Текст: "${link.text}"`);
          if (link.title) console.log(`      Заголовок: "${link.title}"`);
        });
      }
    }

    console.log('\n3️⃣ Поиск скрытых или динамических элементов...');
    
    const hiddenAnalysis = await page.evaluate(() => {
      const results = {
        hiddenElements: 0,
        displayNoneElements: 0,
        visibilityHiddenElements: 0,
        opacityZeroElements: 0,
        offscreenElements: 0
      };
      
      const allElements = document.querySelectorAll('*');
      
      allElements.forEach(el => {
        const style = window.getComputedStyle(el);
        const rect = el.getBoundingClientRect();
        
        if (style.display === 'none') results.displayNoneElements++;
        if (style.visibility === 'hidden') results.visibilityHiddenElements++;
        if (style.opacity === '0') results.opacityZeroElements++;
        if (rect.left < 0 || rect.top < 0 || rect.right > window.innerWidth || rect.bottom > window.innerHeight) {
          results.offscreenElements++;
        }
      });
      
      return results;
    });
    
    console.log(`   Скрытые элементы (display: none): ${hiddenAnalysis.displayNoneElements}`);
    console.log(`   Скрытые элементы (visibility: hidden): ${hiddenAnalysis.visibilityHiddenElements}`);
    console.log(`   Прозрачные элементы (opacity: 0): ${hiddenAnalysis.opacityZeroElements}`);
    console.log(`   Элементы за пределами экрана: ${hiddenAnalysis.offscreenElements}`);

    console.log('\n4️⃣ Поиск потенциальных триггеров загрузки...');
    
    const triggers = await page.evaluate(() => {
      const triggerSelectors = [
        'button[class*="load"]',
        'button[class*="more"]',
        'button[class*="show"]',
        '.load-more',
        '.show-more',
        '.load-products',
        '[data-load]',
        '[data-more]',
        'button:contains("Load")',
        'button:contains("More")',
        'button:contains("Show")'
      ];
      
      const results: Record<string, number> = {};
      
      triggerSelectors.forEach(selector => {
        try {
          results[selector] = document.querySelectorAll(selector).length;
        } catch (e) {
          results[selector] = -1;
        }
      });
      
      return results;
    });
    
    Object.entries(triggers).forEach(([selector, count]) => {
      if (count > 0) {
        console.log(`   ✅ ${selector}: ${count} элементов`);
      }
    });

    console.log('\n5️⃣ Попытка взаимодействия с элементами...');
    
    // Попробуем кликнуть на различные элементы
    const interactionAttempts = [
      { selector: 'button', description: 'Кнопки' },
      { selector: '.filter', description: 'Фильтры' },
      { selector: '.category', description: 'Категории' },
      { selector: '[role="button"]', description: 'Элементы с ролью кнопки' }
    ];
    
    for (const attempt of interactionAttempts) {
      try {
        const count = await page.locator(attempt.selector).count();
        if (count > 0) {
          console.log(`   🔍 ${attempt.description}: найдено ${count} элементов`);
          
          // Попробуем кликнуть на первый элемент
          await page.locator(attempt.selector).first().click({ timeout: 5000 });
          await page.waitForTimeout(2000);
          
          // Проверим, появились ли продукты
          const productCount = await page.locator('a[href*="/products/"]').count();
          if (productCount > 0) {
            console.log(`   ✅ После клика на ${attempt.description} найдено ${productCount} продуктов!`);
            break;
          }
        }
      } catch (e) {
        console.log(`   ❌ Ошибка при взаимодействии с ${attempt.description}: ${e}`);
      }
    }

  } catch (error) {
    console.log(`💥 Ошибка: ${error}`);
  } finally {
    if (browser) {
      await browser.close();
    }
  }

  console.log('\n🏁 Тест альтернативных селекторов завершен');
}

testAlternativeSelectors().catch(console.error);
