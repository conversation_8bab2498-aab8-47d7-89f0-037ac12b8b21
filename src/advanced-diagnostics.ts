import { chromium, <PERSON><PERSON>er, <PERSON> } from 'playwright';

async function runAdvancedDiagnostics() {
  console.log('🔬 FST Scraper - Продвинутая диагностика динамической загрузки');
  console.log('===========================================================\n');

  let browser: Browser | undefined;
  let page: Page | undefined;

  try {
    browser = await chromium.launch({
      headless: true, // Запускаем без интерфейса
      args: ['--no-sandbox', '--disable-dev-shm-usage']
    });

    page = await browser.newPage({
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    });

    const problemUrl = 'https://products.fst.com/global/en/categories/rotary-seals';
    
    console.log('1️⃣ Анализ сетевых запросов при загрузке...');
    
    const networkRequests: Array<{
      url: string, 
      method: string,
      resourceType: string,
      status?: number,
      responseBody?: string
    }> = [];
    
    // Перехватываем все запросы
    page.on('request', (request) => {
      networkRequests.push({
        url: request.url(),
        method: request.method(),
        resourceType: request.resourceType()
      });
    });
    
    page.on('response', async (response) => {
      const request = networkRequests.find(req => req.url === response.url());
      if (request) {
        request.status = response.status();
        
        // Сохраняем тело ответа для API запросов
        if (response.request().resourceType() === 'xhr' || 
            response.request().resourceType() === 'fetch' ||
            response.url().includes('api') ||
            response.url().includes('products')) {
          try {
            request.responseBody = await response.text();
          } catch (e) {
            request.responseBody = 'Failed to read response body';
          }
        }
      }
    });

    console.log('🌐 Загружаем страницу категории...');
    await page.goto(problemUrl, { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });

    console.log('⏳ Ожидание 10 секунд для загрузки динамического контента...');
    await page.waitForTimeout(10000);

    // Анализ API запросов
    const apiRequests = networkRequests.filter(req => 
      req.resourceType === 'xhr' || 
      req.resourceType === 'fetch' ||
      req.url.includes('api') ||
      req.url.includes('products') ||
      req.url.includes('catalog')
    );

    console.log('\n2️⃣ Анализ API запросов:');
    console.log(`📊 Найдено ${apiRequests.length} потенциальных API запросов:`);
    
    apiRequests.forEach((req, i) => {
      const status = req.status ? (req.status >= 200 && req.status < 300 ? '✅' : '❌') : '⏳';
      console.log(`   ${i + 1}. ${status} ${req.method} ${req.resourceType} ${req.url}`);
      
      if (req.responseBody && req.responseBody.length > 0 && req.responseBody.length < 1000) {
        console.log(`      Response: ${req.responseBody.substring(0, 200)}...`);
      } else if (req.responseBody && req.responseBody.length >= 1000) {
        console.log(`      Response: ${req.responseBody.length} characters (large response)`);
      }
    });

    console.log('\n3️⃣ Поиск элементов пагинации и кнопок загрузки...');
    
    const paginationElements = await page.evaluate(() => {
      const selectors = [
        '.pagination',
        '.pager',
        '.load-more',
        '.show-more',
        '[data-pagination]',
        '.next-page',
        '.page-nav',
        'button[class*="load"]',
        'button[class*="more"]',
        'a[class*="next"]',
        'a[class*="page"]'
      ];
      
      const results: Record<string, number> = {};
      selectors.forEach(selector => {
        try {
          results[selector] = document.querySelectorAll(selector).length;
        } catch (e) {
          results[selector] = -1;
        }
      });
      
      return results;
    });
    
    Object.entries(paginationElements).forEach(([selector, count]) => {
      const status = count > 0 ? '✅' : count === 0 ? '⚠️' : '❌';
      console.log(`   ${status} ${selector}: ${count}`);
    });

    console.log('\n4️⃣ Поиск скрытых или динамических элементов...');
    
    const hiddenElements = await page.evaluate(() => {
      const results = {
        hiddenProducts: 0,
        displayNoneProducts: 0,
        visibilityHiddenProducts: 0,
        dataAttributes: [] as string[],
        vueComponents: 0,
        reactComponents: 0
      };
      
      // Поиск скрытых продуктов
      const allElements = document.querySelectorAll('*');
      allElements.forEach(el => {
        const style = window.getComputedStyle(el);
        const text = el.textContent?.toLowerCase() || '';
        
        if (text.includes('product') || el.className.includes('product')) {
          if (style.display === 'none') results.displayNoneProducts++;
          if (style.visibility === 'hidden') results.visibilityHiddenProducts++;
        }
        
        // Поиск data-атрибутов связанных с продуктами
        Array.from(el.attributes).forEach(attr => {
          if (attr.name.startsWith('data-') && 
              (attr.value.includes('product') || attr.name.includes('product'))) {
            results.dataAttributes.push(`${attr.name}="${attr.value}"`);
          }
        });
      });
      
      // Поиск Vue/React компонентов
      results.vueComponents = document.querySelectorAll('[data-v-]').length;
      results.reactComponents = document.querySelectorAll('[data-reactroot], [data-react-]').length;
      
      return results;
    });
    
    console.log(`   Скрытые продукты (display:none): ${hiddenElements.displayNoneProducts}`);
    console.log(`   Скрытые продукты (visibility:hidden): ${hiddenElements.visibilityHiddenProducts}`);
    console.log(`   Vue компоненты: ${hiddenElements.vueComponents}`);
    console.log(`   React компоненты: ${hiddenElements.reactComponents}`);
    console.log(`   Data-атрибуты с продуктами: ${hiddenElements.dataAttributes.length}`);
    
    if (hiddenElements.dataAttributes.length > 0) {
      console.log('   📋 Найденные data-атрибуты:');
      hiddenElements.dataAttributes.slice(0, 5).forEach(attr => {
        console.log(`      ${attr}`);
      });
    }

    console.log('\n5️⃣ Попытка взаимодействия с элементами...');
    
    // Попробуем найти и кликнуть на кнопки загрузки
    const interactionResults = await page.evaluate(async () => {
      const results: string[] = [];
      
      // Поиск кнопок "Load More", "Show More" и т.д.
      const buttons = document.querySelectorAll('button, a');
      const loadButtons: Element[] = [];
      
      buttons.forEach(btn => {
        const text = btn.textContent?.toLowerCase() || '';
        const className = btn.className.toLowerCase();
        
        if (text.includes('load') || text.includes('more') || text.includes('show') ||
            className.includes('load') || className.includes('more')) {
          loadButtons.push(btn);
        }
      });
      
      results.push(`Найдено ${loadButtons.length} потенциальных кнопок загрузки`);
      
      loadButtons.forEach((btn, i) => {
        results.push(`  ${i + 1}. "${btn.textContent?.trim()}" (${btn.tagName.toLowerCase()}.${btn.className})`);
      });
      
      return results;
    });
    
    interactionResults.forEach(result => console.log(`   ${result}`));

    console.log('\n6️⃣ Анализ JavaScript состояния...');
    
    const jsState = await page.evaluate(() => {
      const state = {
        hasVue: typeof (window as any).Vue !== 'undefined',
        hasReact: typeof (window as any).React !== 'undefined',
        hasJQuery: typeof (window as any).$ !== 'undefined',
        hasNuxt: typeof (window as any).$nuxt !== 'undefined',
        hasNext: typeof (window as any).__NEXT_DATA__ !== 'undefined',
        globalVars: Object.keys(window).filter(key => 
          key.includes('product') || 
          key.includes('catalog') || 
          key.includes('api')
        ).slice(0, 10)
      };
      
      return state;
    });
    
    console.log(`   Vue.js: ${jsState.hasVue ? '✅' : '❌'}`);
    console.log(`   React: ${jsState.hasReact ? '✅' : '❌'}`);
    console.log(`   jQuery: ${jsState.hasJQuery ? '✅' : '❌'}`);
    console.log(`   Nuxt.js: ${jsState.hasNuxt ? '✅' : '❌'}`);
    console.log(`   Next.js: ${jsState.hasNext ? '✅' : '❌'}`);
    console.log(`   Глобальные переменные: ${jsState.globalVars.join(', ')}`);

    console.log('\n⏸️  Дополнительное ожидание для завершения всех запросов...');
    await page.waitForTimeout(5000);

  } catch (error) {
    console.log(`💥 Ошибка: ${error}`);
  } finally {
    if (browser) {
      await browser.close();
    }
  }

  console.log('\n🏁 Продвинутая диагностика завершена');
}

runAdvancedDiagnostics().catch(console.error);
