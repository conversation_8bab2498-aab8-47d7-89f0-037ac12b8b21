import { chromium, <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';
import * as fs from 'fs-extra';
import * as path from 'path';
import { ScraperConfig, ProductInfo, CategoryInfo, SubcategoryInfo } from './types';
import { ScrapingLogger } from './logger';

export class FSTScraper {
  private config: ScraperConfig;
  private logger: ScrapingLogger;
  private browser?: Browser;
  private page?: Page;

  constructor(config: ScraperConfig) {
    this.config = {
      delay: 1000,
      maxRetries: 3,
      ...config
    };
    this.logger = new ScrapingLogger(config.logFile);
    
    // Ensure output directory exists
    fs.ensureDirSync(config.outputDir);
  }

  async start(): Promise<void> {
    try {
      await this.initBrowser();
      
      const progress = this.logger.getProgress();
      if (progress.status === 'completed') {
        this.logger.logInfo('Scraping already completed. Use reset() to start over.');
        return;
      }

      this.logger.logInfo('Starting FST product scraper...');
      this.logger.setStatus('running');

      // Get all categories and their product URLs
      const categories = await this.getCategories();
      const allProductUrls = categories.flatMap(cat => cat.productUrls);
      
      this.logger.updateTotalCounts(categories.length, allProductUrls.length);
      this.logger.logInfo(`Found ${categories.length} categories with ${allProductUrls.length} total products`);

      // Scrape products from each category
      for (const category of categories) {
        await this.scrapeCategory(category);
      }

      this.logger.setStatus('completed');
      const stats = this.logger.getCompletionStats();
      this.logger.logInfo(`Scraping completed! Scraped ${stats.completed}/${stats.total} products (${stats.percentage}%)`);

    } catch (error) {
      this.logger.setStatus('error');
      this.logger.logError('Scraping failed', error);
      throw error;
    } finally {
      await this.closeBrowser();
    }
  }

  private async initBrowser(): Promise<void> {
    this.logger.logInfo('Launching browser...');

    // Упрощенные настройки браузера для стабильности
    const maxRetries = 3;
    let attempt = 0;

    while (attempt < maxRetries) {
      try {
        this.browser = await chromium.launch({
          headless: true,
          args: [
            '--no-sandbox',
            '--disable-dev-shm-usage'
          ]
        });

        // Create page with basic settings
        this.page = await this.browser.newPage({
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        });

        // Set viewport to standard desktop resolution
        await this.page.setViewportSize({ width: 1920, height: 1080 });

        // Set longer timeout
        this.page.setDefaultTimeout(60000); // 60 seconds
        this.page.setDefaultNavigationTimeout(60000); // 60 seconds

        this.logger.logInfo('Browser launched successfully');
        return;

      } catch (error) {
        attempt++;
        this.logger.logError(`Failed to launch browser, attempt ${attempt}/${maxRetries}:`, error);

        // Закрываем браузер если он был частично создан
        if (this.browser) {
          try {
            await this.browser.close();
          } catch (e) {
            // Игнорируем ошибки закрытия
          }
          this.browser = undefined;
          this.page = undefined;
        }

        if (attempt >= maxRetries) {
          throw new Error(`Failed to launch browser after ${maxRetries} attempts. Last error: ${error}`);
        }

        // Ждем перед повторной попыткой
        await new Promise(resolve => setTimeout(resolve, 2000 * attempt));
      }
    }
  }

  private async closeBrowser(): Promise<void> {
    if (this.browser) {
      try {
        await this.browser.close();
      } catch (error) {
        this.logger.logError('Error closing browser:', error);
      } finally {
        this.browser = undefined;
        this.page = undefined;
      }
    }
  }

  private async getCategories(): Promise<CategoryInfo[]> {
    if (!this.page) throw new Error('Browser not initialized');

    this.logger.logInfo('Fetching product categories...');
    await this.page.goto(this.config.baseUrl, {
      waitUntil: 'domcontentloaded',
      timeout: 60000
    });

    // Wait for page to fully load
    await this.page.waitForTimeout(5000);

    // ИСПРАВЛЕНИЕ: Сначала получаем основные категории
    const mainCategoryLinks = await this.page.$$eval('a[href*="/categories/"]', links => {
      return links.map(link => ({
        name: link.textContent?.trim() || '',
        url: (link as HTMLAnchorElement).href
      })).filter(cat => {
        // Фильтруем только основные категории (не подкатегории)
        const hasName = cat.name && cat.url;
        const isMainCategory = cat.url.includes('/categories/') &&
                              !cat.url.endsWith('/categories') &&
                              cat.name.length > 2 &&
                              // Исключаем подкатегории (содержат более одного слеша после /categories/)
                              (cat.url.match(/\/categories\/[^\/]+$/));
        return hasName && isMainCategory;
      });
    });

    const categories: CategoryInfo[] = [];

    for (const mainCategoryLink of mainCategoryLinks) {
      try {
        this.logger.logInfo(`Processing main category: ${mainCategoryLink.name}`);

        // Переходим на страницу основной категории
        await this.page.goto(mainCategoryLink.url, {
          waitUntil: 'domcontentloaded',
          timeout: 60000
        });
        await this.page.waitForTimeout(5000);

        // ИСПРАВЛЕНИЕ: Получаем подкатегории из основной категории
        const subcategoryLinks = await this.page.$$eval('.category-tile__link, a.cardTile', links => {
          return links.map(link => ({
            name: link.textContent?.trim() || '',
            url: (link as HTMLAnchorElement).href
          })).filter(subcat => {
            const hasName = subcat.name && subcat.url;
            const isSubcategory = subcat.url.includes('/categories/') &&
                                 subcat.name.length > 2;
            return hasName && isSubcategory;
          });
        });

        this.logger.logInfo(`Found ${subcategoryLinks.length} subcategories in ${mainCategoryLink.name}`);

        // Обрабатываем каждую подкатегорию
        for (const subcategoryLink of subcategoryLinks) {
          try {
            this.logger.logInfo(`Fetching products from subcategory: ${subcategoryLink.name}`);
            const productUrls = await this.getCategoryProducts(subcategoryLink.url);

            categories.push({
              name: `${mainCategoryLink.name} > ${subcategoryLink.name}`,
              url: subcategoryLink.url,
              productUrls,
              scraped: false
            });

            await this.delay();
          } catch (error) {
            this.logger.logError(`Failed to fetch products from subcategory ${subcategoryLink.name}`, error);
          }
        }

      } catch (error) {
        this.logger.logError(`Failed to process main category ${mainCategoryLink.name}`, error);
      }
    }

    return categories;
  }

  private async getCategoryProducts(categoryUrl: string): Promise<string[]> {
    if (!this.page) throw new Error('Browser not initialized');

    const maxRetries = this.config.maxRetries || 3;
    let attempt = 0;

    while (attempt < maxRetries) {
      try {
        this.logger.logInfo(`Loading category: ${categoryUrl} (attempt ${attempt + 1}/${maxRetries})`);

        // Используем более надежную стратегию загрузки
        await this.page.goto(categoryUrl, {
          waitUntil: 'domcontentloaded',
          timeout: 30000
        });

        // ИСПРАВЛЕНИЕ: Увеличиваем время ожидания для загрузки продуктов
        this.logger.logInfo('Waiting for products to load...');
        await this.page.waitForTimeout(12000); // Увеличено до 12 секунд

        // ИСПРАВЛЕНИЕ: Прокручиваем страницу для триггера ленивой загрузки
        await this.page.evaluate(() => {
          window.scrollTo(0, document.body.scrollHeight);
        });
        await this.page.waitForTimeout(3000);

        await this.page.evaluate(() => {
          window.scrollTo(0, 0);
        });
        await this.page.waitForTimeout(2000);

        this.logger.logInfo(`Successfully loaded category: ${categoryUrl}`);
        break;

      } catch (error) {
        attempt++;
        const errorMessage = error instanceof Error ? error.message : String(error);
        this.logger.logError(`Failed to load category ${categoryUrl}, attempt ${attempt}/${maxRetries}: ${errorMessage}`);

        if (attempt >= maxRetries) {
          throw new Error(`Failed to load category after ${maxRetries} attempts: ${categoryUrl}. Error: ${errorMessage}`);
        }

        const retryDelay = 3000 * attempt;
        this.logger.logInfo(`Retrying in ${retryDelay}ms...`);
        await this.delay(retryDelay);
      }
    }

    const productUrls: string[] = [];
    let hasNextPage = true;
    let pageCount = 0;

    while (hasNextPage && pageCount < 50) { // Limit to 50 pages to prevent infinite loops
      try {
        // ИСПРАВЛЕНИЕ: Используем правильный селектор для продуктов
        const pageProductUrls = await this.page.$$eval('a[href*="product"]', links => {
          return Array.from(new Set(
            links.map(link => (link as HTMLAnchorElement).href)
              .filter(url => url.includes('/products/')) // Фильтруем только реальные продукты
          ));
        });

        this.logger.logInfo(`Found ${pageProductUrls.length} products on page ${pageCount + 1}`);
        productUrls.push(...pageProductUrls);
        pageCount++;

        // Check if there's a next page
        const nextButton = await this.page.$('a[aria-label="Next"], .pagination a:last-child, .next-page');
        if (nextButton && await nextButton.isEnabled()) {
          await nextButton.click();
          await this.page.waitForLoadState('domcontentloaded', { timeout: 30000 });
          await this.delay(3000);
        } else {
          hasNextPage = false;
        }
      } catch (error) {
        this.logger.logError(`Error processing page ${pageCount + 1}:`, error);
        hasNextPage = false; // Stop pagination on error
      }
    }

    const uniqueProductUrls = Array.from(new Set(productUrls));
    this.logger.logInfo(`Total unique products found: ${uniqueProductUrls.length}`);
    return uniqueProductUrls;
  }

  private async scrapeCategory(category: CategoryInfo): Promise<void> {
    this.logger.setCurrentCategory(category.name);
    this.logger.logInfo(`Scraping category: ${category.name} (${category.productUrls.length} products)`);

    for (const productUrl of category.productUrls) {
      const productId = this.extractProductId(productUrl);
      
      if (this.logger.isProductCompleted(productId)) {
        this.logger.logInfo(`Skipping already scraped product: ${productId}`);
        continue;
      }

      try {
        const productInfo = await this.scrapeProduct(productUrl, category.name);
        await this.saveProduct(productInfo);
        this.logger.markProductCompleted(productId);
        
        const stats = this.logger.getCompletionStats();
        this.logger.logInfo(`Scraped product ${productId} (${stats.completed}/${stats.total} - ${stats.percentage}%)`);
        
      } catch (error) {
        this.logger.markProductFailed(productId);
        this.logger.logError(`Failed to scrape product ${productId}`, error);
      }
      
      await this.delay();
    }
    
    this.logger.markCategoryCompleted();
    this.logger.logInfo(`Completed category: ${category.name}`);
  }

  private async scrapeProduct(productUrl: string, category: string): Promise<ProductInfo> {
    if (!this.page) throw new Error('Browser not initialized');

    const maxRetries = this.config.maxRetries || 3;
    let attempt = 0;
    
    while (attempt < maxRetries) {
      try {
        this.logger.logInfo(`Loading product page: ${productUrl} (attempt ${attempt + 1}/${maxRetries})`);
        
        await this.page.goto(productUrl, { 
          waitUntil: 'domcontentloaded',
          timeout: 90000  // Увеличен таймаут до 90 секунд
        });
        
        // Дополнительная проверка загрузки контента
        await this.page.waitForSelector('body', { timeout: 30000 });
        
        this.logger.logInfo(`Successfully loaded product page: ${productUrl}`);
        break;
      } catch (error) {
        attempt++;
        this.logger.logError(`Failed to load product ${productUrl}, attempt ${attempt}/${maxRetries}:`, error);
        
        if (attempt >= maxRetries) {
          throw new Error(`Failed to load product after ${maxRetries} attempts: ${productUrl}. Last error: ${error}`);
        }
        
        // Прогрессивное увеличение задержки
        const retryDelay = 2000 * attempt; // 2s, 4s, 6s...
        this.logger.logInfo(`Retrying in ${retryDelay}ms...`);
        await this.delay(retryDelay);
        
        // Попытка перезагрузить страницу перед повторной попыткой
        try {
          await this.page.reload({ timeout: 30000 });
        } catch (reloadError) {
          this.logger.logError('Failed to reload page:', reloadError);
        }
      }
    }
    
    await this.delay(3000); // Wait for page to fully load
    
    const productId = this.extractProductId(productUrl);
    
    // Улучшенное извлечение данных продукта
    const productInfo = await this.page.evaluate((categoryName) => {
      const productData = {
        id: '',
        url: window.location.href,
        title: document.title,
        h1: document.querySelector('h1')?.textContent?.trim() || '',
        description: '',
        specifications: {} as Record<string, string>,
        dimensions: {} as Record<string, string>,
        images: [] as string[],
        breadcrumbs: [] as string[],
        category: categoryName,
        scrapedAt: new Date().toISOString()
      };
      
      // Извлекаем описание
      const descElements = ['.product-description', '.description'];
      for (const selector of descElements) {
        const el = document.querySelector(selector);
        if (el && el.textContent && el.textContent.trim().length > 50) {
          productData.description = el.textContent.trim();
          break;
        }
      }
      
      // Основные характеристики из таблицы деталей
      const mainTableRows = document.querySelectorAll('.product-details-main__table__row');
      mainTableRows.forEach((row, index) => {
        const labelEl = row.querySelector('.product-details-main__table__row__label');
        const valueEl = row.querySelector('.product-details-main__table__row__value');
        
        if (labelEl && valueEl) {
          const key = labelEl.textContent?.trim() || '';
          const value = valueEl.textContent?.trim() || '';
          if (key && value) {
            productData.specifications[key] = value;
          }
        } else {
          const rowText = row.textContent?.trim();
          if (rowText && index === 0) {
            productData.specifications['FST item no.'] = rowText;
          }
        }
      });
      
      // Размеры и технические характеристики
      const dimensionElements = document.querySelectorAll('.dimension-label');
      dimensionElements.forEach(labelEl => {
        const key = labelEl.textContent?.trim();
        if (key) {
          const parent = labelEl.parentElement;
          if (parent) {
            const valueText = parent.textContent?.replace(key, '').trim();
            if (valueText) {
              productData.dimensions[key] = valueText;
            }
          }
        }
      });
      
      // Поиск характеристик по паттернам
      const textContent = document.body.textContent || '';
      const specPatterns = [
        { key: 'Net weight', pattern: /Net weight[:\\s]+([^\\n\\r]*)/i },
        { key: 'Packaging', pattern: /Packaging[:\\s]+([^\\n\\r]*)/i },
        { key: 'Material', pattern: /Material[:\\s]+([^\\n\\r]*)/i },
        { key: 'Brand', pattern: /Brand[:\\s]+([^\\n\\r]*)/i },
        { key: 'REACH', pattern: /REACH[:\\s]+([^\\n\\r]*)/i },
        { key: 'ROHS', pattern: /ROHS[:\\s]+([^\\n\\r]*)/i },
        { key: 'Legacy item no.', pattern: /Legacy item no[.:]?[:\\s]+([^\\n\\r]*)/i }
      ];
      
      for (const {key, pattern} of specPatterns) {
        const match = textContent.match(pattern);
        if (match && match[1]) {
          const value = match[1].trim();
          if (value && value.length < 200) {
            productData.specifications[key] = value;
          }
        }
      }
      
      // Хлебные крошки
      const breadcrumbElements = document.querySelectorAll('.breadcrumb a, .breadcrumb span');
      productData.breadcrumbs = Array.from(breadcrumbElements).map(item => 
        item.textContent?.trim()
      ).filter(Boolean) as string[];
      
      // Изображения
      const images = Array.from(document.querySelectorAll('img'));
      productData.images = images.map(img => {
        const src = img.getAttribute('src') || img.getAttribute('data-src');
        if (src && !src.includes('logo') && !src.includes('icon')) {
          return new URL(src, window.location.href).href;
        }
        return null;
      }).filter(Boolean) as string[];
      
      return productData;
    }, category);
    
    productInfo.id = productId;
    
    // Скачиваем изображения
    if (productInfo.images && productInfo.images.length > 0) {
      productInfo.images = await this.downloadImages(productInfo.images, productId);
    }

    return productInfo;
  }

  private async downloadImages(imageUrls: string[], productId: string): Promise<string[]> {
    const downloadedImages: string[] = [];
    const imagesDir = path.join(this.config.outputDir, 'images', productId);
    
    fs.ensureDirSync(imagesDir);
    
    for (let i = 0; i < imageUrls.length; i++) {
      try {
        const imageUrl = imageUrls[i];
        const response = await fetch(imageUrl);
        
        if (response.ok) {
          const buffer = await response.arrayBuffer();
          const extension = path.extname(new URL(imageUrl).pathname) || '.jpg';
          const filename = `image_${i + 1}${extension}`;
          const filepath = path.join(imagesDir, filename);
          
          await fs.writeFile(filepath, Buffer.from(buffer));
          downloadedImages.push(filepath);
        }
      } catch (error) {
        this.logger.logError(`Failed to download image ${imageUrls[i]} for product ${productId}`, error);
      }
    }
    
    return downloadedImages;
  }

  private async saveProduct(productInfo: ProductInfo): Promise<void> {
    const filename = `product_${productInfo.id}.json`;
    const filepath = path.join(this.config.outputDir, filename);
    
    await fs.writeJson(filepath, productInfo, { spaces: 2 });
  }

  private extractProductId(productUrl: string): string {
    const match = productUrl.match(/\/products\/(\w+)/);
    return match ? match[1] : path.basename(productUrl);
  }

  private async delay(customDelay?: number): Promise<void> {
    const delayTime = customDelay || this.config.delay || 1000;
    if (delayTime > 0) {
      await new Promise(resolve => setTimeout(resolve, delayTime));
    }
  }

  // Utility methods
  public reset(): void {
    this.logger.reset();
    this.logger.logInfo('Scraping progress has been reset');
  }

  public getProgress() {
    return this.logger.getProgress();
  }
}
