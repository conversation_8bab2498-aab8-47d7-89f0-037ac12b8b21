import { chromium } from 'playwright';

async function simpleBrowserTest() {
  console.log('🔍 Простой тест браузера');
  console.log('========================\n');

  let browser;
  let page;

  try {
    console.log('1️⃣ Запуск браузера...');
    browser = await chromium.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-dev-shm-usage']
    });
    console.log('✅ Браузер запущен');

    console.log('2️⃣ Создание страницы...');
    page = await browser.newPage();
    console.log('✅ Страница создана');

    console.log('3️⃣ Переход на страницу...');
    const url = 'https://products.fst.com/global/en/categories/rotary-seals';
    await page.goto(url, { waitUntil: 'domcontentloaded', timeout: 30000 });
    console.log('✅ Страница загружена');

    console.log('4️⃣ Ожидание контента...');
    await page.waitForTimeout(5000);
    console.log('✅ Ожидание завершено');

    console.log('5️⃣ Анализ страницы...');
    const analysis = await page.evaluate(() => {
      return {
        title: document.title,
        url: window.location.href,
        bodyLength: document.body?.textContent?.length || 0,
        allLinks: document.querySelectorAll('a').length,
        productLinks: document.querySelectorAll('a[href*="/products/"]').length,
        hasNuxt: typeof (window as any).$nuxt !== 'undefined',
        scripts: document.querySelectorAll('script').length
      };
    });

    console.log('📊 Результаты анализа:');
    console.log(`   Заголовок: ${analysis.title}`);
    console.log(`   URL: ${analysis.url}`);
    console.log(`   Размер контента: ${analysis.bodyLength} символов`);
    console.log(`   Всего ссылок: ${analysis.allLinks}`);
    console.log(`   Ссылки на продукты: ${analysis.productLinks}`);
    console.log(`   Nuxt.js: ${analysis.hasNuxt ? 'Да' : 'Нет'}`);
    console.log(`   Скриптов: ${analysis.scripts}`);

    if (analysis.productLinks === 0) {
      console.log('\n⚠️ ПРОБЛЕМА: Продукты не найдены!');
      console.log('Возможные причины:');
      console.log('1. Продукты загружаются через JavaScript после загрузки страницы');
      console.log('2. Используется другой селектор для ссылок на продукты');
      console.log('3. Продукты загружаются через API запросы');
      console.log('4. Требуется взаимодействие с элементами страницы');
    }

  } catch (error) {
    console.log(`❌ Ошибка: ${error}`);
  } finally {
    if (browser) {
      await browser.close();
    }
  }

  console.log('\n🏁 Тест завершен');
}

simpleBrowserTest().catch(console.error);
