import { chromium } from 'playwright';

async function testSubcategories() {
  console.log('🔍 Тест подкатегорий для поиска продуктов');
  console.log('======================================\n');

  let browser;
  let page;

  try {
    browser = await chromium.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-dev-shm-usage']
    });

    page = await browser.newPage();
    
    // Сначала получим список подкатегорий
    console.log('1️⃣ Получение списка подкатегорий...');
    const mainUrl = 'https://products.fst.com/global/en/categories/rotary-seals';
    await page.goto(mainUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
    await page.waitForTimeout(5000);

    // Закрываем cookie popup если есть
    try {
      await page.click('[data-testid="uc-accept-all-button"]', { timeout: 3000 });
      console.log('✅ Cookie popup закрыт');
    } catch (e) {
      console.log('ℹ️ Cookie popup не найден или уже закрыт');
    }

    const subcategories = await page.evaluate(() => {
      const cards = document.querySelectorAll('[class*="card"] a');
      const subcats: Array<{name: string, url: string}> = [];
      
      cards.forEach(card => {
        const link = card as HTMLAnchorElement;
        const text = link.textContent?.trim() || '';
        const href = link.href;
        
        if (href && href.includes('/categories/') && text && text.length > 2) {
          subcats.push({
            name: text.substring(0, 50), // Ограничиваем длину
            url: href
          });
        }
      });
      
      return subcats;
    });

    console.log(`📊 Найдено ${subcategories.length} подкатегорий:`);
    subcategories.forEach((subcat, i) => {
      console.log(`   ${i + 1}. ${subcat.name} -> ${subcat.url}`);
    });

    // Тестируем каждую подкатегорию
    console.log('\n2️⃣ Тестирование подкатегорий на наличие продуктов...');
    
    const results: Array<{
      name: string,
      url: string,
      productCount: number,
      error?: string
    }> = [];

    for (const subcat of subcategories.slice(0, 5)) { // Ограничиваем до 5 для скорости
      try {
        console.log(`\n🔍 Тестируем: ${subcat.name}`);
        console.log(`   URL: ${subcat.url}`);
        
        await page.goto(subcat.url, { 
          waitUntil: 'domcontentloaded', 
          timeout: 30000 
        });
        
        // Ждем загрузки контента
        await page.waitForTimeout(8000);
        
        // Пробуем разные селекторы для продуктов
        const productAnalysis = await page.evaluate(() => {
          const selectors = [
            'a[href*="/products/"]',
            'a[href*="/product/"]',
            'a[href*="product"]',
            '.product-link',
            '.product-item a',
            '.product-card a',
            '.product a',
            '.item a',
            '.card a',
            '[data-product]',
            '[data-product-id]'
          ];
          
          const analysis = {
            totalLinks: document.querySelectorAll('a').length,
            bodyLength: document.body?.textContent?.length || 0,
            title: document.title,
            productCounts: {} as Record<string, number>
          };
          
          selectors.forEach(selector => {
            try {
              analysis.productCounts[selector] = document.querySelectorAll(selector).length;
            } catch (e) {
              analysis.productCounts[selector] = -1;
            }
          });
          
          return analysis;
        });
        
        console.log(`   📄 Заголовок: ${productAnalysis.title}`);
        console.log(`   📊 Всего ссылок: ${productAnalysis.totalLinks}`);
        console.log(`   📊 Размер контента: ${productAnalysis.bodyLength} символов`);
        
        // Найдем лучший селектор
        const bestSelector = Object.entries(productAnalysis.productCounts)
          .filter(([_, count]) => count > 0)
          .sort(([_, a], [__, b]) => b - a)[0];
        
        if (bestSelector) {
          console.log(`   ✅ Найдены продукты! Селектор: ${bestSelector[0]} (${bestSelector[1]} элементов)`);
          
          // Получим примеры ссылок
          const sampleLinks = await page.evaluate((selector) => {
            const elements = document.querySelectorAll(selector);
            const links: Array<{href: string, text: string}> = [];
            
            for (let i = 0; i < Math.min(5, elements.length); i++) {
              const el = elements[i] as HTMLAnchorElement;
              links.push({
                href: el.href || '',
                text: (el.textContent || '').trim().substring(0, 50)
              });
            }
            
            return links;
          }, bestSelector[0]);
          
          console.log('   📋 Примеры найденных ссылок:');
          sampleLinks.forEach((link, i) => {
            console.log(`      ${i + 1}. ${link.href}`);
            if (link.text) console.log(`         Текст: "${link.text}"`);
          });
          
          results.push({
            name: subcat.name,
            url: subcat.url,
            productCount: bestSelector[1]
          });
        } else {
          console.log('   ⚠️ Продукты не найдены');
          results.push({
            name: subcat.name,
            url: subcat.url,
            productCount: 0
          });
        }
        
      } catch (error) {
        console.log(`   ❌ Ошибка: ${error}`);
        results.push({
          name: subcat.name,
          url: subcat.url,
          productCount: 0,
          error: String(error)
        });
      }
    }

    // Итоговый отчет
    console.log('\n3️⃣ Итоговый отчет:');
    console.log('==================');
    
    const successfulCategories = results.filter(r => r.productCount > 0);
    const totalProducts = results.reduce((sum, r) => sum + r.productCount, 0);
    
    console.log(`📊 Подкатегорий с продуктами: ${successfulCategories.length}/${results.length}`);
    console.log(`📊 Общее количество найденных продуктов: ${totalProducts}`);
    
    if (successfulCategories.length > 0) {
      console.log('\n✅ Подкатегории с продуктами:');
      successfulCategories.forEach(cat => {
        console.log(`   • ${cat.name}: ${cat.productCount} продуктов`);
        console.log(`     ${cat.url}`);
      });
      
      console.log('\n🎯 РЕШЕНИЕ НАЙДЕНО!');
      console.log('Продукты находятся в подкатегориях, а не в основной категории.');
      console.log('Скрапер должен:');
      console.log('1. Сначала получить список подкатегорий из основной категории');
      console.log('2. Затем перейти в каждую подкатегорию');
      console.log('3. Извлечь продукты из подкатегорий');
    } else {
      console.log('\n❌ Продукты не найдены ни в одной подкатегории');
      console.log('Возможно, требуется дополнительное взаимодействие или ожидание');
    }

  } catch (error) {
    console.log(`💥 Критическая ошибка: ${error}`);
  } finally {
    if (browser) {
      await browser.close();
    }
  }

  console.log('\n🏁 Тест подкатегорий завершен');
}

testSubcategories().catch(console.error);
