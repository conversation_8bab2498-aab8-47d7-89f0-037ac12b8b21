import { chromium, <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';

async function runDiagnostics() {
  console.log('🔍 FST Scraper - Комплексная диагностика');
  console.log('=====================================\n');

  let browser: Browser | undefined;
  let page: Page | undefined;

  try {
    // 1. Тест запуска браузера
    console.log('1️⃣ Тестирование запуска браузера...');
    try {
      browser = await chromium.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-dev-shm-usage']
      });
      console.log('✅ Браузер запущен успешно\n');
    } catch (error) {
      console.log(`❌ Ошибка запуска браузера: ${error}`);
      return;
    }

    // 2. Создание страницы
    console.log('2️⃣ Создание страницы...');
    try {
      page = await browser.newPage({
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      });
      console.log('✅ Страница создана\n');
    } catch (error) {
      console.log(`❌ Ошибка создания страницы: ${error}`);
      return;
    }

    // 3. Тест базового URL
    console.log('3️⃣ Тестирование базового URL...');
    const baseUrl = 'https://products.fst.com/global/en';
    
    try {
      const response = await page.goto(baseUrl, {
        waitUntil: 'domcontentloaded',
        timeout: 30000
      });
      
      console.log(`✅ Статус ответа: ${response?.status()}`);
      console.log(`✅ URL загружен: ${page.url()}`);
      
      const title = await page.title();
      console.log(`✅ Заголовок страницы: ${title}\n`);
    } catch (error) {
      console.log(`❌ Ошибка загрузки базового URL: ${error}\n`);
      return;
    }

    // 4. Поиск категорий
    console.log('4️⃣ Поиск ссылок на категории...');
    const categoryLinks = await page.$$eval('a[href*="/categories/"]', links => {
      return links.map(link => ({
        text: link.textContent?.trim() || '',
        href: (link as HTMLAnchorElement).href
      })).slice(0, 5);
    });
    
    console.log(`✅ Найдено ${categoryLinks.length} ссылок на категории:`);
    categoryLinks.forEach((link, i) => {
      console.log(`   ${i + 1}. ${link.text} -> ${link.href}`);
    });
    console.log();

    // 5. Тест проблемной категории
    console.log('5️⃣ Тестирование проблемной категории...');
    const problemUrl = 'https://products.fst.com/global/en/categories/rotary-seals';
    
    console.log(`🔗 Тестируем: ${problemUrl}`);
    
    // Тест с разными стратегиями загрузки
    const strategies = [
      { name: 'domcontentloaded', waitUntil: 'domcontentloaded' as const },
      { name: 'load', waitUntil: 'load' as const },
      { name: 'networkidle', waitUntil: 'networkidle' as const }
    ];

    for (const strategy of strategies) {
      try {
        console.log(`   Пробуем стратегию: ${strategy.name}...`);
        
        const startTime = Date.now();
        const response = await page.goto(problemUrl, {
          waitUntil: strategy.waitUntil,
          timeout: 20000
        });
        const loadTime = Date.now() - startTime;
        
        console.log(`   ✅ ${strategy.name}: ${response?.status()} (${loadTime}ms)`);
        
        // Проверяем содержимое
        const hasContent = await page.evaluate(() => {
          return {
            title: document.title,
            bodyLength: document.body?.textContent?.length || 0,
            hasProducts: document.querySelectorAll('a[href*="/products/"]').length > 0
          };
        });
        
        console.log(`   📄 Контент: title="${hasContent.title}", body=${hasContent.bodyLength} chars, products=${hasContent.hasProducts}`);
        break;
        
      } catch (error) {
        console.log(`   ❌ ${strategy.name}: ${error instanceof Error ? error.message : error}`);
      }
    }

    // 6. Тест сети и производительности
    console.log('\n6️⃣ Диагностика сети...');
    
    let requestCount = 0;
    let failedRequests = 0;
    
    page.on('request', () => requestCount++);
    page.on('requestfailed', (req) => {
      failedRequests++;
      console.log(`   ❌ Неудачный запрос: ${req.url()} - ${req.failure()?.errorText}`);
    });
    
    await page.reload({ timeout: 15000 });
    
    console.log(`   📊 Всего запросов: ${requestCount}`);
    console.log(`   📊 Неудачных запросов: ${failedRequests}`);

    // 7. Проверка JavaScript ошибок
    console.log('\n7️⃣ Проверка JavaScript ошибок...');
    
    const jsErrors: string[] = [];
    page.on('pageerror', (error) => {
      jsErrors.push(error.message);
    });
    
    await page.waitForTimeout(3000);
    
    if (jsErrors.length > 0) {
      console.log(`   ❌ Найдено ${jsErrors.length} JS ошибок:`);
      jsErrors.forEach(error => console.log(`      - ${error}`));
    } else {
      console.log('   ✅ JavaScript ошибок не найдено');
    }

    // 8. Детальная диагностика поиска продуктов
    console.log('\n8️⃣ Детальная диагностика поиска продуктов...');

    try {
      await page.goto(problemUrl, {
        waitUntil: 'domcontentloaded',
        timeout: 30000
      });

      // Ждем загрузки контента
      await page.waitForTimeout(5000);

      // Детальный анализ DOM
      const domAnalysis = await page.evaluate(() => {
        const analysis = {
          allLinks: document.querySelectorAll('a').length,
          productLinks: document.querySelectorAll('a[href*="/products/"]').length,
          categoryLinks: document.querySelectorAll('a[href*="/categories/"]').length,
          bodyText: document.body?.textContent?.length || 0,
          hasProductGrid: !!document.querySelector('.product-grid, .products-grid, .product-list'),
          hasLoadingIndicator: !!document.querySelector('.loading, .spinner, [data-loading]'),
          scripts: document.querySelectorAll('script').length,
          iframes: document.querySelectorAll('iframe').length
        };

        // Поиск различных селекторов продуктов
        const productSelectors = [
          'a[href*="/products/"]',
          '.product-item a',
          '.product-card a',
          '.product-link',
          '[data-product-url]',
          '.product a',
          '.item a'
        ];

        const selectorResults: Record<string, number> = {};
        productSelectors.forEach(selector => {
          try {
            selectorResults[selector] = document.querySelectorAll(selector).length;
          } catch (e) {
            selectorResults[selector] = -1;
          }
        });

        return { ...analysis, selectorResults };
      });

      console.log('   📊 Анализ DOM:');
      console.log(`      Всего ссылок: ${domAnalysis.allLinks}`);
      console.log(`      Ссылки на продукты: ${domAnalysis.productLinks}`);
      console.log(`      Ссылки на категории: ${domAnalysis.categoryLinks}`);
      console.log(`      Размер текста: ${domAnalysis.bodyText} символов`);
      console.log(`      Есть сетка продуктов: ${domAnalysis.hasProductGrid}`);
      console.log(`      Есть индикатор загрузки: ${domAnalysis.hasLoadingIndicator}`);
      console.log(`      Скриптов: ${domAnalysis.scripts}`);
      console.log(`      Фреймов: ${domAnalysis.iframes}`);

      console.log('\n   🔍 Результаты поиска по селекторам:');
      Object.entries(domAnalysis.selectorResults).forEach(([selector, count]) => {
        const status = count > 0 ? '✅' : count === 0 ? '⚠️' : '❌';
        console.log(`      ${status} ${selector}: ${count}`);
      });

    } catch (error) {
      console.log(`   ❌ Ошибка анализа DOM: ${error}`);
    }

    // 9. Тест ожидания динамического контента
    console.log('\n9️⃣ Тест ожидания динамического контента...');

    try {
      await page.goto(problemUrl, {
        waitUntil: 'domcontentloaded',
        timeout: 30000
      });

      console.log('   ⏳ Ожидание загрузки динамического контента...');

      // Пробуем разные стратегии ожидания
      const waitStrategies = [
        {
          name: 'Ожидание исчезновения загрузчика',
          action: async () => {
            await page.waitForFunction(() => {
              const loader = document.querySelector('.loading, .spinner, [data-loading="true"]');
              return !loader || loader.style.display === 'none';
            }, { timeout: 15000 });
          }
        },
        {
          name: 'Ожидание появления продуктов',
          action: async () => {
            await page.waitForFunction(() => {
              return document.querySelectorAll('a[href*="/products/"]').length > 0;
            }, { timeout: 15000 });
          }
        },
        {
          name: 'Ожидание стабилизации DOM',
          action: async () => {
            let lastCount = 0;
            let stableCount = 0;

            while (stableCount < 3) {
              await page.waitForTimeout(1000);
              const currentCount = await page.evaluate(() => document.querySelectorAll('a').length);

              if (currentCount === lastCount) {
                stableCount++;
              } else {
                stableCount = 0;
                lastCount = currentCount;
              }
            }
          }
        }
      ];

      for (const strategy of waitStrategies) {
        try {
          console.log(`   🔄 ${strategy.name}...`);
          await strategy.action();

          const productCount = await page.evaluate(() =>
            document.querySelectorAll('a[href*="/products/"]').length
          );

          console.log(`   ✅ ${strategy.name}: найдено ${productCount} продуктов`);

          if (productCount > 0) {
            break;
          }

        } catch (error) {
          console.log(`   ❌ ${strategy.name}: ${error instanceof Error ? error.message : error}`);
        }
      }

    } catch (error) {
      console.log(`   ❌ Ошибка ожидания контента: ${error}`);
    }

    // 10. Анализ сетевых запросов
    console.log('\n🔟 Анализ сетевых запросов...');

    const networkRequests: Array<{url: string, status: number, resourceType: string}> = [];

    page.on('response', (response) => {
      networkRequests.push({
        url: response.url(),
        status: response.status(),
        resourceType: response.request().resourceType()
      });
    });

    await page.reload({ timeout: 30000 });
    await page.waitForTimeout(5000);

    const apiRequests = networkRequests.filter(req =>
      req.url.includes('api') ||
      req.url.includes('json') ||
      req.resourceType === 'xhr' ||
      req.resourceType === 'fetch'
    );

    console.log(`   📊 Всего запросов: ${networkRequests.length}`);
    console.log(`   📊 API/AJAX запросов: ${apiRequests.length}`);

    if (apiRequests.length > 0) {
      console.log('   🌐 API запросы:');
      apiRequests.slice(0, 10).forEach(req => {
        const status = req.status >= 200 && req.status < 300 ? '✅' : '❌';
        console.log(`      ${status} ${req.status} ${req.resourceType} ${req.url}`);
      });
    }

  } catch (error) {
    console.log(`💥 Критическая ошибка: ${error}`);
  } finally {
    if (browser) {
      await browser.close();
    }
  }

  console.log('\n🏁 Диагностика завершена');
}

runDiagnostics().catch(console.error);