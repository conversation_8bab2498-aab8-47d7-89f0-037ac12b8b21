import { chromium } from 'playwright';

async function finalSubcategoryTest() {
  console.log('🎯 Финальный тест подкатегорий с правильными селекторами');
  console.log('====================================================\n');

  let browser;
  let page;

  try {
    browser = await chromium.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-dev-shm-usage']
    });

    page = await browser.newPage();
    
    console.log('1️⃣ Получение подкатегорий с правильным селектором...');
    const mainUrl = 'https://products.fst.com/global/en/categories/rotary-seals';
    await page.goto(mainUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
    await page.waitForTimeout(8000);

    // Получаем подкатегории с правильным селектором
    const subcategories = await page.evaluate(() => {
      const selectors = [
        '.category-tile__link',
        'a.cardTile',
        '.category-tile a'
      ];
      
      let bestResults: Array<{name: string, url: string}> = [];
      
      for (const selector of selectors) {
        try {
          const elements = document.querySelectorAll(selector);
          const results: Array<{name: string, url: string}> = [];
          
          elements.forEach(el => {
            const link = el as HTMLAnchorElement;
            const text = (link.textContent || '').trim();
            const href = link.href;
            
            if (href && href.includes('/categories/rotary-seals/') && text && text.length > 2) {
              results.push({
                name: text.substring(0, 100),
                url: href
              });
            }
          });
          
          if (results.length > bestResults.length) {
            bestResults = results;
          }
        } catch (e) {
          // Ignore errors
        }
      }
      
      return bestResults;
    });

    console.log(`📊 Найдено ${subcategories.length} подкатегорий:`);
    subcategories.forEach((subcat, i) => {
      console.log(`   ${i + 1}. ${subcat.name}`);
      console.log(`      → ${subcat.url}`);
    });

    if (subcategories.length === 0) {
      console.log('❌ Подкатегории не найдены. Завершаем тест.');
      return;
    }

    console.log('\n2️⃣ Тестирование подкатегорий на наличие продуктов...');
    
    const productResults: Array<{
      name: string,
      url: string,
      productCount: number,
      bestSelector?: string,
      sampleProducts?: Array<{href: string, text: string}>
    }> = [];

    // Тестируем первые 3 подкатегории для скорости
    for (const subcat of subcategories.slice(0, 3)) {
      try {
        console.log(`\n🔍 Тестируем: ${subcat.name}`);
        
        await page.goto(subcat.url, { 
          waitUntil: 'domcontentloaded', 
          timeout: 30000 
        });
        
        // Ждем загрузки контента дольше
        console.log('   ⏳ Ожидание загрузки продуктов...');
        await page.waitForTimeout(12000);

        // Пробуем прокрутить страницу для триггера ленивой загрузки
        await page.evaluate(() => {
          window.scrollTo(0, document.body.scrollHeight);
        });
        await page.waitForTimeout(3000);
        
        await page.evaluate(() => {
          window.scrollTo(0, 0);
        });
        await page.waitForTimeout(2000);

        // Тестируем различные селекторы продуктов
        const productAnalysis = await page.evaluate(() => {
          const productSelectors = [
            'a[href*="/products/"]',
            'a[href*="/product/"]',
            'a[href*="product"]',
            '.product-link',
            '.product-item a',
            '.product-card a',
            '.product a',
            '.item a',
            '.card a',
            '.tile a',
            '.grid-item a',
            '.catalog-item a',
            '[data-product] a',
            '[data-product-id]',
            '.sku-container a',
            '.product-tile a',
            '.result-item a'
          ];
          
          const analysis = {
            title: document.title,
            bodyLength: document.body?.textContent?.length || 0,
            totalLinks: document.querySelectorAll('a').length,
            productCounts: {} as Record<string, number>,
            bestSelector: '',
            bestCount: 0
          };
          
          productSelectors.forEach(selector => {
            try {
              const count = document.querySelectorAll(selector).length;
              analysis.productCounts[selector] = count;
              
              if (count > analysis.bestCount) {
                analysis.bestCount = count;
                analysis.bestSelector = selector;
              }
            } catch (e) {
              analysis.productCounts[selector] = -1;
            }
          });
          
          return analysis;
        });
        
        console.log(`   📄 Заголовок: ${productAnalysis.title}`);
        console.log(`   📊 Всего ссылок: ${productAnalysis.totalLinks}`);
        console.log(`   📊 Размер контента: ${productAnalysis.bodyLength} символов`);
        
        if (productAnalysis.bestCount > 0) {
          console.log(`   ✅ Найдены продукты! Селектор: ${productAnalysis.bestSelector} (${productAnalysis.bestCount} элементов)`);
          
          // Получаем примеры продуктов
          const sampleProducts = await page.evaluate((selector) => {
            const elements = document.querySelectorAll(selector);
            const products: Array<{href: string, text: string}> = [];
            
            for (let i = 0; i < Math.min(5, elements.length); i++) {
              const el = elements[i] as HTMLAnchorElement;
              const href = el.href || '';
              const text = (el.textContent || '').trim().substring(0, 80);
              
              if (href && (href.includes('/product') || text.length > 0)) {
                products.push({ href, text });
              }
            }
            
            return products;
          }, productAnalysis.bestSelector);
          
          console.log('   📋 Примеры найденных продуктов:');
          sampleProducts.forEach((product, i) => {
            console.log(`      ${i + 1}. ${product.href}`);
            if (product.text) console.log(`         "${product.text}"`);
          });
          
          productResults.push({
            name: subcat.name,
            url: subcat.url,
            productCount: productAnalysis.bestCount,
            bestSelector: productAnalysis.bestSelector,
            sampleProducts
          });
        } else {
          console.log('   ⚠️ Продукты не найдены');
          console.log('   📊 Результаты по селекторам:');
          Object.entries(productAnalysis.productCounts).forEach(([selector, count]) => {
            if (count > 0) {
              console.log(`      ✅ ${selector}: ${count}`);
            }
          });
          
          productResults.push({
            name: subcat.name,
            url: subcat.url,
            productCount: 0
          });
        }
        
      } catch (error) {
        console.log(`   ❌ Ошибка: ${error}`);
        productResults.push({
          name: subcat.name,
          url: subcat.url,
          productCount: 0
        });
      }
    }

    // Итоговый отчет
    console.log('\n3️⃣ ИТОГОВЫЙ ОТЧЕТ');
    console.log('==================');
    
    const successfulCategories = productResults.filter(r => r.productCount > 0);
    const totalProducts = productResults.reduce((sum, r) => sum + r.productCount, 0);
    
    console.log(`📊 Протестировано подкатегорий: ${productResults.length}`);
    console.log(`📊 Подкатегорий с продуктами: ${successfulCategories.length}`);
    console.log(`📊 Общее количество найденных продуктов: ${totalProducts}`);
    
    if (successfulCategories.length > 0) {
      console.log('\n🎯 УСПЕХ! Найдены продукты в подкатегориях:');
      successfulCategories.forEach(cat => {
        console.log(`\n✅ ${cat.name}: ${cat.productCount} продуктов`);
        console.log(`   URL: ${cat.url}`);
        console.log(`   Лучший селектор: ${cat.bestSelector}`);
        
        if (cat.sampleProducts && cat.sampleProducts.length > 0) {
          console.log('   Примеры продуктов:');
          cat.sampleProducts.slice(0, 3).forEach((product, i) => {
            console.log(`   ${i + 1}. ${product.href}`);
          });
        }
      });
      
      console.log('\n🔧 РЕКОМЕНДАЦИИ ДЛЯ ИСПРАВЛЕНИЯ СКРАПЕРА:');
      console.log('1. Изменить логику получения категорий:');
      console.log('   - Использовать селектор: .category-tile__link или a.cardTile');
      console.log('   - Фильтровать ссылки, содержащие /categories/rotary-seals/');
      console.log('');
      console.log('2. Для каждой подкатегории:');
      console.log('   - Переходить на страницу подкатегории');
      console.log('   - Ждать загрузки контента (10-15 секунд)');
      console.log('   - Прокручивать страницу для триггера ленивой загрузки');
      const bestSelectors = [...new Set(successfulCategories.map(c => c.bestSelector).filter(Boolean))];
      console.log(`   - Использовать селекторы: ${bestSelectors.join(', ')}`);
      console.log('');
      console.log('3. Структура должна быть:');
      console.log('   Основная категория → Подкатегории → Продукты');
      
    } else {
      console.log('\n❌ Продукты не найдены ни в одной подкатегории');
      console.log('Возможные причины:');
      console.log('- Требуется больше времени ожидания');
      console.log('- Нужно взаимодействие с элементами страницы');
      console.log('- Продукты загружаются через API после взаимодействия');
    }

  } catch (error) {
    console.log(`💥 Критическая ошибка: ${error}`);
  } finally {
    if (browser) {
      await browser.close();
    }
  }

  console.log('\n🏁 Финальный тест завершен');
}

finalSubcategoryTest().catch(console.error);
