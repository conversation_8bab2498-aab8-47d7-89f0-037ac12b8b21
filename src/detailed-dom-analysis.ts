import { chromium } from 'playwright';

async function detailedDomAnalysis() {
  console.log('🔍 Детальный анализ DOM структуры');
  console.log('================================\n');

  let browser;
  let page;

  try {
    browser = await chromium.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-dev-shm-usage']
    });

    page = await browser.newPage();
    
    const url = 'https://products.fst.com/global/en/categories/rotary-seals';
    await page.goto(url, { waitUntil: 'domcontentloaded', timeout: 30000 });
    await page.waitForTimeout(10000);

    console.log('1️⃣ Анализ всех ссылок на странице...');
    
    const allLinks = await page.evaluate(() => {
      const links = document.querySelectorAll('a');
      const linkData: Array<{
        href: string,
        text: string,
        className: string,
        id: string,
        parentClass: string,
        hasProduct: boolean,
        hasCategory: boolean
      }> = [];
      
      links.forEach(link => {
        const href = link.href || '';
        const text = (link.textContent || '').trim();
        const className = link.className || '';
        const id = link.id || '';
        const parentClass = link.parentElement?.className || '';
        
        linkData.push({
          href,
          text: text.substring(0, 100), // Ограничиваем длину
          className,
          id,
          parentClass,
          hasProduct: href.toLowerCase().includes('product'),
          hasCategory: href.includes('/categories/')
        });
      });
      
      return linkData;
    });

    console.log(`📊 Всего ссылок найдено: ${allLinks.length}`);
    
    // Анализируем ссылки на категории
    const categoryLinks = allLinks.filter(link => link.hasCategory);
    console.log(`📊 Ссылок на категории: ${categoryLinks.length}`);
    
    if (categoryLinks.length > 0) {
      console.log('\n📋 Ссылки на категории:');
      categoryLinks.forEach((link, i) => {
        console.log(`   ${i + 1}. ${link.text}`);
        console.log(`      URL: ${link.href}`);
        console.log(`      Class: ${link.className}`);
        console.log(`      Parent Class: ${link.parentClass}`);
        console.log('');
      });
    }

    // Анализируем ссылки с "product" в URL
    const productLinks = allLinks.filter(link => link.hasProduct);
    console.log(`📊 Ссылок с "product" в URL: ${productLinks.length}`);
    
    if (productLinks.length > 0) {
      console.log('\n📋 Ссылки с "product":');
      productLinks.forEach((link, i) => {
        console.log(`   ${i + 1}. ${link.text}`);
        console.log(`      URL: ${link.href}`);
        console.log('');
      });
    }

    console.log('\n2️⃣ Анализ структуры DOM...');
    
    const domStructure = await page.evaluate(() => {
      const structure = {
        mainContainers: [] as string[],
        cardElements: [] as string[],
        listElements: [] as string[],
        gridElements: [] as string[],
        categoryElements: [] as string[]
      };
      
      // Поиск основных контейнеров
      const containerSelectors = ['.container', '.main', '.content', '.wrapper', '#main', '#content'];
      containerSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        if (elements.length > 0) {
          structure.mainContainers.push(`${selector}: ${elements.length} элементов`);
        }
      });
      
      // Поиск элементов карточек
      const cardSelectors = ['.card', '.tile', '.item', '[class*="card"]', '[class*="tile"]'];
      cardSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        if (elements.length > 0) {
          structure.cardElements.push(`${selector}: ${elements.length} элементов`);
        }
      });
      
      // Поиск списков
      const listSelectors = ['.list', '.grid', '.catalog', '[class*="list"]', '[class*="grid"]'];
      listSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        if (elements.length > 0) {
          structure.listElements.push(`${selector}: ${elements.length} элементов`);
        }
      });
      
      // Поиск элементов с "category" в классе
      const categorySelectors = ['[class*="category"]', '[class*="Category"]'];
      categorySelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        if (elements.length > 0) {
          structure.categoryElements.push(`${selector}: ${elements.length} элементов`);
        }
      });
      
      return structure;
    });

    console.log('📊 Основные контейнеры:');
    domStructure.mainContainers.forEach(container => {
      console.log(`   ${container}`);
    });
    
    console.log('\n📊 Элементы карточек:');
    domStructure.cardElements.forEach(card => {
      console.log(`   ${card}`);
    });
    
    console.log('\n📊 Элементы списков/сеток:');
    domStructure.listElements.forEach(list => {
      console.log(`   ${list}`);
    });
    
    console.log('\n📊 Элементы категорий:');
    domStructure.categoryElements.forEach(cat => {
      console.log(`   ${cat}`);
    });

    console.log('\n3️⃣ Поиск текста "Simmerring" и других подкатегорий...');
    
    const textSearch = await page.evaluate(() => {
      const searchTerms = ['Simmerring', 'Radiamatic', 'Radial Shaft', 'Cassette', 'Axial'];
      const results: Array<{
        term: string,
        elements: Array<{
          tagName: string,
          className: string,
          text: string,
          href?: string
        }>
      }> = [];
      
      searchTerms.forEach(term => {
        const elements = Array.from(document.querySelectorAll('*')).filter(el => {
          const text = el.textContent || '';
          return text.includes(term);
        });
        
        const elementData = elements.slice(0, 5).map(el => ({
          tagName: el.tagName.toLowerCase(),
          className: el.className || '',
          text: (el.textContent || '').trim().substring(0, 100),
          href: el.tagName.toLowerCase() === 'a' ? (el as HTMLAnchorElement).href : undefined
        }));
        
        if (elementData.length > 0) {
          results.push({
            term,
            elements: elementData
          });
        }
      });
      
      return results;
    });

    textSearch.forEach(result => {
      console.log(`\n🔍 Поиск "${result.term}": найдено ${result.elements.length} элементов`);
      result.elements.forEach((el, i) => {
        console.log(`   ${i + 1}. <${el.tagName}> class="${el.className}"`);
        console.log(`      Текст: "${el.text}"`);
        if (el.href) console.log(`      Ссылка: ${el.href}`);
      });
    });

    console.log('\n4️⃣ Анализ HTML структуры страницы...');
    
    const htmlStructure = await page.evaluate(() => {
      const body = document.body;
      const structure = {
        bodyClasses: body.className,
        childrenCount: body.children.length,
        mainSections: [] as string[]
      };
      
      // Анализируем основные секции
      Array.from(body.children).forEach((child, i) => {
        if (i < 10) { // Ограничиваем до 10 элементов
          const tagName = child.tagName.toLowerCase();
          const className = child.className || '';
          const id = child.id || '';
          structure.mainSections.push(`${tagName}${id ? '#' + id : ''}${className ? '.' + className.split(' ')[0] : ''}`);
        }
      });
      
      return structure;
    });

    console.log(`📊 Классы body: ${htmlStructure.bodyClasses}`);
    console.log(`📊 Дочерних элементов body: ${htmlStructure.childrenCount}`);
    console.log('📊 Основные секции:');
    htmlStructure.mainSections.forEach(section => {
      console.log(`   ${section}`);
    });

    console.log('\n5️⃣ Попытка найти подкатегории через различные подходы...');
    
    // Попробуем найти элементы, которые могут содержать подкатегории
    const subcategorySearch = await page.evaluate(() => {
      const approaches = [
        {
          name: 'Поиск по тексту "Simmerring"',
          selector: '*',
          filter: (el: Element) => (el.textContent || '').includes('Simmerring')
        },
        {
          name: 'Поиск ссылок в карточках',
          selector: '.card a, [class*="card"] a',
          filter: () => true
        },
        {
          name: 'Поиск всех ссылок с категориями',
          selector: 'a[href*="/categories/"]',
          filter: () => true
        },
        {
          name: 'Поиск по data-атрибутам',
          selector: '[data-*]',
          filter: (el: Element) => {
            const attrs = Array.from(el.attributes);
            return attrs.some(attr => 
              attr.name.startsWith('data-') && 
              (attr.value.includes('category') || attr.value.includes('product'))
            );
          }
        }
      ];
      
      const results: Array<{
        approach: string,
        count: number,
        samples: Array<{
          tagName: string,
          className: string,
          text: string,
          href?: string
        }>
      }> = [];
      
      approaches.forEach(approach => {
        try {
          const elements = Array.from(document.querySelectorAll(approach.selector))
            .filter(approach.filter);
          
          const samples = elements.slice(0, 3).map(el => ({
            tagName: el.tagName.toLowerCase(),
            className: el.className || '',
            text: (el.textContent || '').trim().substring(0, 80),
            href: el.tagName.toLowerCase() === 'a' ? (el as HTMLAnchorElement).href : undefined
          }));
          
          results.push({
            approach: approach.name,
            count: elements.length,
            samples
          });
        } catch (e) {
          results.push({
            approach: approach.name,
            count: -1,
            samples: []
          });
        }
      });
      
      return results;
    });

    subcategorySearch.forEach(result => {
      console.log(`\n📋 ${result.approach}: ${result.count} элементов`);
      if (result.samples.length > 0) {
        result.samples.forEach((sample, i) => {
          console.log(`   ${i + 1}. <${sample.tagName}> "${sample.text}"`);
          if (sample.href) console.log(`      → ${sample.href}`);
        });
      }
    });

  } catch (error) {
    console.log(`💥 Ошибка: ${error}`);
  } finally {
    if (browser) {
      await browser.close();
    }
  }

  console.log('\n🏁 Детальный анализ DOM завершен');
}

detailedDomAnalysis().catch(console.error);
