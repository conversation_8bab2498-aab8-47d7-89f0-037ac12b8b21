import { chromium } from 'playwright';

async function testFinalPagination() {
  console.log('🎯 Финальный тест пагинации с обработкой cookie popup');
  console.log('==================================================\n');

  let browser;
  let page;

  try {
    browser = await chromium.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-dev-shm-usage']
    });

    page = await browser.newPage({
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    });
    
    await page.setViewportSize({ width: 1920, height: 1080 });
    page.setDefaultTimeout(60000);

    const testUrl = 'https://products.fst.com/global/en/categories/rotary-seals/radial-shaft-seal-simmerring';
    
    console.log(`🌐 Переходим на: ${testUrl}`);
    await page.goto(testUrl, { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });
    
    await page.waitForTimeout(5000);

    console.log('1️⃣ Обработка cookie popup...');
    
    // Закрываем cookie popup если есть
    const cookieSelectors = [
      '[data-testid="uc-accept-all-button"]',
      '#usercentrics-cmp-ui button[data-testid="uc-accept-all-button"]',
      '.uc-accept-all-button',
      'button:has-text("Accept all")',
      'button:has-text("Принять все")',
      '#usercentrics-cmp-ui .uc-list-button:first-child'
    ];

    let cookieClosed = false;
    for (const selector of cookieSelectors) {
      try {
        const cookieButton = await page.$(selector);
        if (cookieButton) {
          console.log(`✅ Найдена кнопка cookie: ${selector}`);
          await cookieButton.click();
          await page.waitForTimeout(2000);
          cookieClosed = true;
          console.log('✅ Cookie popup закрыт');
          break;
        }
      } catch (e) {
        // Продолжаем поиск
      }
    }

    if (!cookieClosed) {
      console.log('ℹ️ Cookie popup не найден или уже закрыт');
    }

    await page.waitForTimeout(3000);

    console.log('2️⃣ Проверка продуктов на странице 1...');
    
    // Прокрутка для загрузки контента
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight);
    });
    await page.waitForTimeout(3000);
    
    await page.evaluate(() => {
      window.scrollTo(0, 0);
    });
    await page.waitForTimeout(2000);

    const page1Products = await page.evaluate(() => {
      const productUrls: string[] = [];
      const elements = document.querySelectorAll('a[href*="/products/"]');
      elements.forEach(el => {
        const href = (el as HTMLAnchorElement).href;
        if (href) productUrls.push(href);
      });
      return Array.from(new Set(productUrls));
    });

    console.log(`📦 Страница 1: найдено ${page1Products.length} уникальных продуктов`);

    console.log('\n3️⃣ Переход на страницу 2...');
    
    try {
      // Используем найденный селектор
      const nextButton = await page.$('.k-pager button[title*="next"]');
      
      if (nextButton) {
        const isDisabled = await nextButton.evaluate(el => 
          el.classList.contains('k-disabled') || el.classList.contains('disabled')
        );
        
        console.log(`🔍 Кнопка Next найдена. Отключена: ${isDisabled}`);
        
        if (!isDisabled) {
          console.log('🔄 Кликаем на кнопку Next...');
          
          // Пробуем разные способы клика
          try {
            await nextButton.click({ force: true });
          } catch (e) {
            console.log('⚠️ Обычный клик не сработал, пробуем JavaScript клик...');
            await nextButton.evaluate(el => (el as HTMLElement).click());
          }
          
          await page.waitForTimeout(5000);
          
          // Проверяем, что страница изменилась
          const currentPageInfo = await page.evaluate(() => {
            const selectedButton = document.querySelector('.k-pager .k-selected');
            const ariaLabel = document.querySelector('.k-pager')?.getAttribute('aria-label') || '';
            return {
              selectedPage: selectedButton?.textContent?.trim() || '',
              ariaLabel
            };
          });
          
          console.log(`📄 Текущая страница: ${currentPageInfo.selectedPage}`);
          console.log(`📊 Aria-label: ${currentPageInfo.ariaLabel}`);
          
          // Проверяем продукты на новой странице
          const page2Products = await page.evaluate(() => {
            const productUrls: string[] = [];
            const elements = document.querySelectorAll('a[href*="/products/"]');
            elements.forEach(el => {
              const href = (el as HTMLAnchorElement).href;
              if (href) productUrls.push(href);
            });
            return Array.from(new Set(productUrls));
          });
          
          console.log(`📦 Страница 2: найдено ${page2Products.length} продуктов`);
          
          // Проверяем, что продукты отличаются
          const differentProducts = page2Products.filter(url => !page1Products.includes(url));
          console.log(`🔄 Новых продуктов: ${differentProducts.length}`);
          
          if (differentProducts.length > 0 && currentPageInfo.selectedPage === '2') {
            console.log('✅ УСПЕХ! Пагинация работает корректно!');
            console.log('📋 Примеры новых продуктов:');
            differentProducts.slice(0, 3).forEach((url, i) => {
              console.log(`   ${i + 1}. ${url}`);
            });
            
            console.log('\n4️⃣ Тест перехода на страницу 3...');
            
            // Пробуем перейти на страницу 3
            const nextButton2 = await page.$('.k-pager button[title*="next"]');
            if (nextButton2) {
              const isDisabled2 = await nextButton2.evaluate(el => 
                el.classList.contains('k-disabled') || el.classList.contains('disabled')
              );
              
              if (!isDisabled2) {
                try {
                  await nextButton2.click({ force: true });
                } catch (e) {
                  await nextButton2.evaluate(el => (el as HTMLElement).click());
                }
                
                await page.waitForTimeout(5000);
                
                const page3Info = await page.evaluate(() => {
                  const selectedButton = document.querySelector('.k-pager .k-selected');
                  return selectedButton?.textContent?.trim() || '';
                });
                
                console.log(`📄 Страница 3: ${page3Info}`);
                
                if (page3Info === '3') {
                  console.log('✅ Переход на страницу 3 успешен!');
                  
                  console.log('\n🎯 ИТОГОВЫЕ РЕЗУЛЬТАТЫ:');
                  console.log('✅ Селектор кнопки Next: .k-pager button[title*="next"]');
                  console.log('✅ Пагинация работает корректно');
                  console.log('✅ Можно переходить между страницами');
                  console.log('✅ Продукты на разных страницах отличаются');
                  console.log('✅ Cookie popup обрабатывается');
                  
                } else {
                  console.log('⚠️ Переход на страницу 3 не удался');
                }
              } else {
                console.log('⚠️ Кнопка Next на странице 2 отключена');
              }
            }
            
          } else {
            console.log('⚠️ Продукты не изменились или страница не переключилась');
          }
          
        } else {
          console.log('⚠️ Кнопка Next отключена - возможно, только одна страница');
        }
      } else {
        console.log('❌ Кнопка Next не найдена');
      }
      
    } catch (error) {
      console.log(`❌ Ошибка при переходе на страницу 2: ${error}`);
    }

  } catch (error) {
    console.log(`💥 Ошибка: ${error}`);
  } finally {
    if (browser) {
      await browser.close();
    }
  }

  console.log('\n🏁 Тест завершен');
}

testFinalPagination().catch(console.error);
