import { chromium } from 'playwright';

async function testPaginationSelectors() {
  console.log('🔍 Детальный тест селекторов пагинации');
  console.log('====================================\n');

  let browser;
  let page;

  try {
    browser = await chromium.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-dev-shm-usage']
    });

    page = await browser.newPage({
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    });
    
    await page.setViewportSize({ width: 1920, height: 1080 });
    page.setDefaultTimeout(60000);

    const testUrl = 'https://products.fst.com/global/en/categories/rotary-seals/radial-shaft-seal-simmerring';
    
    console.log(`🌐 Переходим на: ${testUrl}`);
    await page.goto(testUrl, { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });
    
    await page.waitForTimeout(8000);

    console.log('1️⃣ Детальный анализ элементов пагинации...');
    
    const paginationAnalysis = await page.evaluate(() => {
      const analysis = {
        kPagerElements: [] as Array<{selector: string, text: string, classes: string, href?: string}>,
        allPaginationLinks: [] as Array<{text: string, classes: string, href?: string, tagName: string}>,
        nextButtonCandidates: [] as Array<{text: string, classes: string, href?: string, tagName: string}>
      };
      
      // Анализ всех элементов с k-pager
      const kPagerElements = document.querySelectorAll('.k-pager *');
      kPagerElements.forEach(el => {
        const text = el.textContent?.trim() || '';
        const classes = el.className || '';
        const href = el.tagName.toLowerCase() === 'a' ? (el as HTMLAnchorElement).href : undefined;

        if (text || classes) {
          const firstClass = typeof classes === 'string' && classes ? classes.split(' ')[0] : '';
          analysis.kPagerElements.push({
            selector: `${el.tagName.toLowerCase()}${firstClass ? '.' + firstClass : ''}`,
            text,
            classes,
            href
          });
        }
      });
      
      // Поиск всех ссылок в пагинации
      const paginationContainers = document.querySelectorAll('.k-pager, .k-pager-numbers-wrap, .pagination');
      paginationContainers.forEach(container => {
        const links = container.querySelectorAll('a, span, button');
        links.forEach(link => {
          const text = link.textContent?.trim() || '';
          const classes = link.className || '';
          const href = link.tagName.toLowerCase() === 'a' ? (link as HTMLAnchorElement).href : undefined;
          
          analysis.allPaginationLinks.push({
            text,
            classes,
            href,
            tagName: link.tagName.toLowerCase()
          });
          
          // Поиск кандидатов на кнопку "Next"
          if (text.includes('›') || text.includes('>') || text.includes('next') || 
              classes.includes('next') || classes.includes('forward')) {
            analysis.nextButtonCandidates.push({
              text,
              classes,
              href,
              tagName: link.tagName.toLowerCase()
            });
          }
        });
      });
      
      return analysis;
    });

    console.log('📊 Элементы в .k-pager:');
    paginationAnalysis.kPagerElements.forEach((el, i) => {
      console.log(`   ${i + 1}. ${el.selector} "${el.text}" (${el.classes})`);
      if (el.href) console.log(`      → ${el.href}`);
    });

    console.log('\n📊 Все ссылки пагинации:');
    paginationAnalysis.allPaginationLinks.forEach((link, i) => {
      if (i < 10) { // Показываем первые 10
        console.log(`   ${i + 1}. <${link.tagName}> "${link.text}" (${link.classes})`);
        if (link.href) console.log(`      → ${link.href}`);
      }
    });

    console.log('\n📊 Кандидаты на кнопку "Next":');
    paginationAnalysis.nextButtonCandidates.forEach((btn, i) => {
      console.log(`   ${i + 1}. <${btn.tagName}> "${btn.text}" (${btn.classes})`);
      if (btn.href) console.log(`      → ${btn.href}`);
    });

    console.log('\n2️⃣ Тестирование различных селекторов Next...');
    
    const nextSelectors = [
      '.k-pager .k-link:last-child',
      '.k-pager-numbers-wrap .k-link:last-child',
      '.k-pager a:last-child',
      '.k-pager [title*="next"]',
      '.k-pager [title*="Next"]',
      '.k-pager a[href*="page=2"]',
      '.k-pager .k-i-arrow-e',
      '.k-pager .k-icon',
      'a[title="Go to the next page"]',
      'a[aria-label="Go to the next page"]'
    ];

    for (const selector of nextSelectors) {
      try {
        const element = await page.$(selector);
        if (element) {
          const elementInfo = await element.evaluate(el => ({
            text: el.textContent?.trim() || '',
            classes: el.className || '',
            href: el.tagName.toLowerCase() === 'a' ? (el as HTMLAnchorElement).href : '',
            disabled: el.classList.contains('k-state-disabled') || 
                     el.classList.contains('disabled') ||
                     el.getAttribute('aria-disabled') === 'true'
          }));
          
          console.log(`✅ ${selector}:`);
          console.log(`   Текст: "${elementInfo.text}"`);
          console.log(`   Классы: ${elementInfo.classes}`);
          console.log(`   Ссылка: ${elementInfo.href}`);
          console.log(`   Отключена: ${elementInfo.disabled}`);
          
          // Если элемент не отключен, попробуем кликнуть
          if (!elementInfo.disabled && elementInfo.href) {
            console.log(`   🔄 Пробуем кликнуть...`);
            
            try {
              await element.click();
              await page.waitForLoadState('domcontentloaded', { timeout: 10000 });
              await page.waitForTimeout(3000);
              
              const currentUrl = page.url();
              console.log(`   ✅ Переход успешен! Новый URL: ${currentUrl}`);
              
              // Проверяем, что мы на другой странице
              if (currentUrl !== testUrl) {
                console.log(`   🎉 НАЙДЕН РАБОЧИЙ СЕЛЕКТОР: ${selector}`);
                break;
              }
              
            } catch (clickError) {
              console.log(`   ❌ Ошибка клика: ${clickError}`);
            }
          }
        } else {
          console.log(`❌ ${selector}: не найден`);
        }
      } catch (error) {
        console.log(`❌ ${selector}: ошибка - ${error}`);
      }
    }

    console.log('\n3️⃣ Анализ структуры HTML пагинации...');
    
    const htmlStructure = await page.evaluate(() => {
      const pager = document.querySelector('.k-pager');
      if (pager) {
        return {
          innerHTML: pager.innerHTML.substring(0, 500) + '...',
          outerHTML: pager.outerHTML.substring(0, 300) + '...'
        };
      }
      return null;
    });

    if (htmlStructure) {
      console.log('📄 HTML структура .k-pager:');
      console.log(htmlStructure.outerHTML);
    }

  } catch (error) {
    console.log(`💥 Ошибка: ${error}`);
  } finally {
    if (browser) {
      await browser.close();
    }
  }

  console.log('\n🏁 Тест завершен');
}

testPaginationSelectors().catch(console.error);
